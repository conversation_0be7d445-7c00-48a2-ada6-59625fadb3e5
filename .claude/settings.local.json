{"permissions": {"allow": ["WebFetch(domain:github.com)", "WebSearch", "WebFetch(domain:pypi.org)", "WebFetch(domain:developers.home-assistant.io)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "<PERSON><PERSON>(pip show:*)", "WebFetch(domain:librespeed.org)", "WebFetch(domain:raw.githubusercontent.com)", "WebFetch(domain:speedtest.lax.i.gtxent.com)", "WebFetch(domain:api.github.com)", "Bash(/tmp/librespeed-cli:*)", "<PERSON><PERSON>(touch:*)", "Bash(git init:*)", "Bash(git add:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(python -m pytest tests/ -v)", "Bash(python3 -m pytest --version)", "Bash(python3 -m pip install:*)", "<PERSON><PERSON>(python3:*)", "Bash(./venv/bin/pip install pytest pytest-homeassistant-custom-component aiohttp async_timeout)", "Bash(./venv/bin/pytest tests/ -v --tb=short)", "Bash(./venv/bin/pytest tests/ -v --tb=short -x)", "Bash(./venv/bin/pytest tests/ -v --tb=line)", "Bash(./venv/bin/pytest tests/test_config_flow.py -v --tb=line)", "Bash(./venv/bin/pytest tests/test_init.py::test_coordinator_manual_speed_test -v --tb=short)", "Bash(./venv/bin/pytest tests/ -v --tb=no)", "Bash(./venv/bin/pytest tests/test_config_flow.py::test_options_flow -v --tb=short)", "Bash(./venv/bin/pip show pytest-homeassistant-custom-component)", "Bash(./venv/bin/python3:*)", "Bash(./venv/bin/pip list)", "Bash(./venv/bin/pytest tests/ --cov=custom_components.librespeed --cov-report=term-missing --no-cov-on-fail)", "Bash(./venv/bin/pytest tests/test_coordinator.py tests/test_error_handling.py -v --tb=short)", "Bash(./venv/bin/pytest tests/test_init.py tests/test_sensor.py tests/test_button.py tests/test_binary_sensor.py tests/test_config_flow.py tests/test_diagnostics.py --cov=custom_components.librespeed --cov-report=term)", "Bash(./venv/bin/pytest tests/ --cov=custom_components.librespeed --cov-report=term)", "Bash(./venv/bin/pytest tests/ --cov=custom_components.librespeed --cov-report=term --cov-report=html)", "Bash(./venv/bin/pytest tests/test_performance.py::test_optimize_json_parsing_with_orjson -xvs)", "Bash(git reset:*)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py --cov=custom_components.librespeed.librespeed_cli --cov-report=term)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py::test_parse_cli_output -xvs)", "Bash(./venv/bin/pytest tests/ -q)", "Bash(./venv/bin/pytest tests/test_diagnostics.py::test_diagnostics_data -xvs)", "Bash(./venv/bin/pytest tests/ --cov=custom_components.librespeed --cov-report=term --tb=no -q)", "Bash(./venv/bin/pytest tests/test_diagnostics.py tests/test_librespeed_cli.py -v --tb=short)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py::test_create_custom_server_json -xvs)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py::test_run_speed_test_with_custom_server -xvs)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py -q --tb=no)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py::test_download_cli_success -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py::test_get_servers -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_librespeed_client.py::test_get_servers_success -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_coordinator.py::test_coordinator_retry_logic -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_coordinator.py::test_coordinator_first_refresh_skip -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_coordinator.py::test_coordinator_concurrent_test_prevention -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_error_handling.py::test_malformed_json_response -xvs --tb=short)", "Bash(git commit:*)", "Bash(./venv/bin/pytest tests/test_error_handling.py::test_cli_command_injection_prevention -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_librespeed_client.py::test_test_download_backend_detection -xvs --tb=short)", "Bash(./venv/bin/pytest tests/ -q --tb=no)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py::test_download_cli_failure -xvs --tb=short)", "Bash(./venv/bin/pytest tests/ --cov=custom_components.librespeed --cov-report=term-missing --tb=no -q)", "Bash(./venv/bin/pytest tests/test_init_extended.py -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_sensor_extended.py -xvs --tb=short)", "Bash(./venv/bin/pytest tests/ -q --co)", "Bash(./venv/bin/pytest tests/test_comprehensive_coverage.py -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_init_coverage.py -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_init_coverage.py -q --tb=no)", "Bash(./venv/bin/pytest tests/test_init_coverage.py::test_coordinator_with_existing_data_on_concurrent_test -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_sensor_attributes.py -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_sensor_attributes.py -q --tb=no)", "Bash(./venv/bin/pytest tests/test_sensor_attributes.py::test_last_test_sensor_attributes -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_sensor_attributes.py::test_data_uploaded_sensor -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_sensor_attributes.py tests/test_config_flow_options.py tests/test_init_coverage.py -q --tb=no)", "Bash(./venv/bin/pytest tests/test_config_flow_options.py::test_options_flow -xvs --tb=short)", "Bash(./venv/bin/pytest tests/ -q --tb=no -W default)", "Bash(./venv/bin/pytest tests/test_init.py -q --disable-warnings -W default::DeprecationWarning -W default::PendingDeprecationWarning)", "Bash(./venv/bin/pytest tests/test_init.py --tb=no -rw)", "Bash(./venv/bin/pytest tests/ -q --tb=no -rw)", "Bash(python -W default -m pytest tests/test_init_coverage.py -v --tb=short)", "Bash(./venv/bin/pytest tests/ --tb=no -ra)", "Bash(./venv/bin/pytest tests/)", "Bash(./venv/bin/pytest tests/ -q -W default)", "Bash(./venv/bin/pytest tests/test_init.py -vvs)", "Bash(./venv/bin/pytest tests/test_config_flow.py --cov=custom_components.librespeed.config_flow --cov-report=term-missing --tb=no -q)", "Bash(./venv/bin/pytest tests/test_config_flow_extended.py -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_config_flow_extended.py -q --tb=no)", "Bash(./venv/bin/pytest tests/test_sensor_setup.py::test_sensor_setup -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_config_flow_coverage.py -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_sensor.py -xvs --tb=short -k \"sensor_setup\")", "Bash(./venv/bin/pytest tests/test_lifetime_sensors.py::test_format_data_size -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_lifetime_sensors.py::test_lifetime_sensors_accumulation -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_lifetime_sensors.py -xvs --tb=short)", "Bash(./venv/bin/pytest tests/ --tb=no -rw)", "Bash(./venv/bin/pytest tests/ -W default)", "Bash(./venv/bin/pytest tests/test_init.py -v --disable-warnings --capture=no)", "Bash(./venv/bin/pytest tests/ --tb=no -q --capture=no)", "Bash(./venv/bin/pytest tests/test_init.py -q --no-disable-warnings)", "Bash(python -m pytest tests/test_init.py -q -W default --tb=no)", "Bash(./venv/bin/pytest tests/test_init.py::test_coordinator_manual_speed_test -xvs)", "Bash(PYTHONWARNINGS=default ./venv/bin/pytest tests/test_init.py -q)", "Bash(export PYTHONWARNINGS=default)", "Bash(./venv/bin/pytest tests/test_init.py -q)", "Bash(./venv/bin/pytest tests/ --co -q)", "Bash(./venv/bin/pytest tests/test_lifetime_persistence.py -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_session_lifecycle.py -xvs)", "Bash(./venv/bin/pytest tests/test_init_coverage.py::test_unload_entry_without_session -xvs)", "Bash(./venv/bin/pytest tests/test_init_coverage.py::test_unload_entry_without_session -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_init_coverage.py::test_cli_backend_setup -xvs --tb=short)", "Bash(./venv/bin/pytest tests/ -q --tb=no -k \"not slow\")", "<PERSON><PERSON>(curl:*)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py::test_cli_initialization -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_coordinator.py::test_coordinator_initialization -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_sensor.py::test_download_speed_sensor -xvs --tb=short)", "Bash(./venv/bin/pytest tests/ -q --tb=no --no-header --no-summary -q)", "<PERSON><PERSON>(python:*)", "Bash(./venv/bin/pytest tests/test_binary_sensor.py::test_binary_sensor_state -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_sensor.py::test_data_transferred_sensors -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_lifetime_sensors.py::test_lifetime_download_sensor -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_binary_sensor.py::test_binary_sensor_state -xvs --tb=line)", "Bash(./venv/bin/pytest tests/test_sensor.py::test_sensor_device_info -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_button.py::test_button_availability -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_config_flow.py::test_form_automatic_server -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_config_flow.py::test_form_specific_server -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_config_flow.py::test_form_specific_server -vs --tb=short)", "Bash(./venv/bin/pytest tests/test_binary_sensor.py::test_binary_sensor_attributes -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_session_lifecycle.py::test_cli_backend_no_session_created -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_init.py::test_coordinator_update -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_init.py::test_coordinator_update -xvs)", "Bash(./venv/bin/pytest tests/test_init.py::test_coordinator_update -xvs --tb=no)", "Bash(./venv/bin/pytest tests/test_config_flow.py -xvs --tb=no)", "Bash(./venv/bin/pytest tests/test_config_flow.py::test_options_flow -xvs --tb=short)", "Bash(ha core:*)", "Bash(./venv/bin/pytest tests/test_librespeed_client.py::test_upload_chunk_success -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_error_handling.py::test_network_timeout_handling -xvs --tb=short)", "WebFetch(domain:www.hacs.xyz)", "Bash(./venv/bin/pytest tests/test_config_flow.py -q --tb=no)", "Bash(./venv/bin/pytest tests/test_config_flow.py::test_options_flow -xvs --tb=long)", "Bash(./venv/bin/pytest tests/ -q --tb=no -k \"cli or client\")", "Bash(./venv/bin/pytest tests/test_error_handling.py::test_memory_cleanup_on_failure -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_librespeed_client.py::test_custom_server_url_handling -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_librespeed_client.py::test_backend_detection -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py -xvs --tb=short -k \"test_run_speed_test_invalid_output or test_run_cli_command_timeout\")", "Bash(./venv/bin/pytest tests/test_librespeed_client.py::test_run_speed_test_server_not_found -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_librespeed_client.py::test_get_servers_failure -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_error_handling.py::test_setup_entry_session_creation_failure -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_init_coverage.py::test_coordinator_unexpected_error -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_init_coverage.py::test_get_servers_error_handling -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py::test_download_cli_success -xvs --tb=line)", "Bash(./venv/bin/pytest tests/test_librespeed_client.py::test_test_latency_failure -xvs --tb=line)", "Bash(./venv/bin/pytest tests/test_lifetime_persistence.py::test_lifetime_data_storage_error_handling -xvs --tb=line)", "Bash(./venv/bin/pytest tests/test_error_handling.py::test_invalid_server_response -xvs --tb=line)", "Bash(./venv/bin/pytest tests/test_error_handling.py::test_cli_command_injection_prevention -xvs --tb=line)", "Bash(./venv/bin/pytest tests/test_lifetime_persistence.py::test_lifetime_data_storage_error_handling -xvs --tb=short)", "Bash(tree:*)", "Bash(./venv/bin/pytest tests/ --tb=no -q)", "Read(/home/<USER>/repository/development-repos/librespeed-ha/venv/lib/python3.13/site-packages/homeassistant/components/nextcloud/**)", "Read(/home/<USER>/repository/development-repos/librespeed-ha/venv/lib/python3.13/site-packages/homeassistant/components/nextcloud/**)", "Bash(./venv/bin/pytest tests/test_lifetime_sensors.py -xvs)", "Bash(./venv/bin/pytest tests/test_lifetime_sensors.py -q --tb=no)", "Bash(./venv/bin/pytest tests/test_lifetime_persistence.py tests/test_init.py::test_coordinator_update -q --tb=no)", "Bash(./venv/bin/pytest tests/test_lifetime_persistence.py::test_lifetime_data_persistence -xvs --tb=line)", "Bash(./venv/bin/pytest tests/test_lifetime_sensors.py tests/test_init.py::test_coordinator_update tests/test_lifetime_persistence.py -q --tb=no)", "Bash(./venv/bin/pytest tests/test_sensor.py -q --tb=no -k \"jitter\")", "Bash(./venv/bin/pytest tests/test_sensor.py -q --tb=no)", "Bash(git checkout:*)", "Bash(/config/custom_components/librespeed/bin/librespeed-cli:*)", "Bash(./venv/bin/python:*)", "Bash(./venv/bin/pytest tests/test_librespeed_cli.py -q)", "Bash(./venv/bin/pytest tests/test_init_coverage.py::test_get_servers_error_handling -v)", "Bash(git rm:*)", "Bash(./venv/bin/pytest tests/ -q --tb=no -x)", "WebFetch(domain:www.home-assistant.io)", "Bash(./venv/bin/pytest tests/test_repairs.py -v)", "Bash(./venv/bin/pytest tests/test_repairs.py::TestCoordinatorRepairIntegration::test_custom_server_failure_creates_repair_issue -v)", "Bash(./venv/bin/pytest tests/test_repairs.py::TestCoordinatorRepairIntegration::test_custom_server_failure_creates_repair_issue -v -s)", "<PERSON><PERSON>(cat:*)", "Bash(./venv/bin/pytest tests/test_config_flow.py -q)", "Bash(./venv/bin/pytest tests/test_error_handling.py::test_setup_entry_session_creation_failure -xvs --tb=line)", "Read(/home/<USER>/repository/core/homeassistant/components/nut/**)", "Read(/home/<USER>/repository/core/homeassistant/components/nut/**)", "Read(/home/<USER>/repository/core/homeassistant/components/nut/**)", "Read(/home/<USER>/repository/core/homeassistant/components/nut/**)", "Read(/home/<USER>/repository/core/homeassistant/components/nut/**)", "Read(/home/<USER>/repository/core/homeassistant/components/nut/**)", "Read(/home/<USER>/repository/core/homeassistant/components/nut/**)", "Read(/home/<USER>/repository/core/homeassistant/components/nut/**)", "Read(/home/<USER>/repository/core/homeassistant/components/nut/**)", "Read(/home/<USER>/repository/core/homeassistant/components/nut/**)", "Read(/home/<USER>/repository/core/homeassistant/components/nut/**)", "Bash(./venv/bin/pytest tests/test_coordinator.py::test_coordinator_update_success -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_coordinator.py tests/test_init.py::test_coordinator_manual_speed_test -q --tb=no)", "Bash(./venv/bin/pytest tests/test_binary_sensor.py::test_binary_sensor_device_info -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_binary_sensor.py::test_binary_sensor_device_info -xvs --tb=line)", "Bash(./venv/bin/pytest tests/test_init_coverage.py::test_cli_backend_setup -xvs --tb=no)", "Bash(./venv/bin/pytest tests/test_init.py tests/test_coordinator.py tests/test_sensor.py tests/test_button.py tests/test_binary_sensor.py -q --tb=no)", "Bash(./venv/bin/pytest tests/test_button.py::test_button_attributes -xvs --tb=short)", "Bash(./venv/bin/pytest tests/test_binary_sensor.py::test_binary_sensor_state -xvs --tb=no)"], "deny": [], "ask": [], "defaultMode": "acceptEdits", "additionalDirectories": ["/home/<USER>/repository/speedtest-cli"]}}