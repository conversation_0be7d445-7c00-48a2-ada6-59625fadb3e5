# LibreSpeed Integration Architecture

## Overview
This integration provides comprehensive network speed testing for Home Assistant using LibreSpeed, with a focus on accuracy, reliability, and user flexibility.

## Key Design Decisions

### 1. Dual Backend Architecture
**Decision:** Support both CLI and Native Python backends

**Rationale:**
- **CLI Backend (Primary)**: The official LibreSpeed CLI provides the most accurate results, matching what users see on librespeed.org
- **Native Python (Fallback)**: Not all platforms support the CLI (32-bit systems, some Docker containers), so we need a fallback
- **Automatic Selection**: CLI is preferred when available, Python backend ensures universal compatibility

### 2. Global Locking Mechanism
**Decision:** Implement a global lock preventing concurrent tests across all instances

**Rationale:**
- Speed tests saturate bandwidth - running multiple simultaneously gives inaccurate results
- Users may have multiple instances (different servers) but should only test one at a time
- Lock includes queue system - instances wait their turn rather than failing

### 3. Ten Sensor Entities
**Decision:** Provide 10 different sensor entities rather than fewer sensors with attributes

**Rationale:**
- **User Feedback**: Users explicitly requested visibility of all metrics
- **Automation**: Each metric as a sensor allows for easier automation triggers
- **History**: Home Assistant tracks sensor history automatically
- **Specific Metrics**:
  - Download/Upload/Ping: Core metrics everyone needs
  - Jitter: Critical for gaming/video conferencing users
  - Data Transferred: Helps users monitor test bandwidth usage
  - Server Name: Debugging and verification
  - Lifetime Stats: Long-term bandwidth monitoring

### 4. Custom Server Support
**Decision:** Allow user-specified LibreSpeed servers with optional certificate verification bypass

**Rationale:**
- Many users self-host LibreSpeed servers in homelabs
- Internal servers often use self-signed certificates
- Skip cert verify is optional and user must explicitly enable
- URL validation prevents injection attacks

### 5. Circuit Breaker Pattern
**Decision:** Stop attempting tests after 10 consecutive failures

**Rationale:**
- Prevents endless retry loops consuming resources
- Creates repair issue to notify user
- Manual test or repair flow can reset the breaker
- Returns last known good data when circuit is open

### 6. Repair Flows
**Decision:** Implement actionable repair flows that actually fix problems

**Rationale:**
- **CLI Download**: Can retry download if it fails
- **Failure Reset**: Resets failure counter to allow retries
- **Circuit Reset**: Closes open circuit breaker
- Better than just showing errors - provides solutions

### 7. Configuration Complexity
**Decision:** 460-line config flow with multiple paths

**Rationale:**
- **Discovery**: Fetches and displays available servers
- **Selection**: Automatic, specific server, or custom URL
- **Validation**: Ensures configuration is valid before saving
- **Options Flow**: Allows runtime reconfiguration without re-setup
- **Backend Selection**: Automatically determines available backends

### 8. Binary Management
**Decision:** Download and manage CLI binary at runtime

**Rationale:**
- Packaging binaries with integration would bloat size
- Different platforms need different binaries
- GitHub releases provide official, signed binaries
- Automatic updates when new CLI versions release
- Fallback to Python if download fails

## Security Model

### Implemented Protections:
1. **Command Injection**: Server IDs bounded 0-10000
2. **URL Validation**: Regex validation for custom URLs
3. **Path Traversal**: Binary only written to specific directory
4. **Resource Limits**: Circuit breaker prevents resource exhaustion

### Accepted Risks:
1. **Self-Signed Certs**: Users can bypass cert verification (explicit opt-in)
2. **Binary Execution**: CLI backend runs external binary (required for accuracy)
3. **Network Access**: Integration makes external HTTP requests

## Performance Optimizations

1. **Connection Pooling**: 200 total, 100 per host
2. **DNS Caching**: 5-minute TTL
3. **Read Buffer**: 256KB for high-speed connections
4. **Concurrent Streams**: 6 download, 3 upload (matches CLI)
5. **Exponential Backoff**: On failures with jitter

## Data Flow

1. **Trigger**: Manual (button) or automatic (interval)
2. **Lock Acquisition**: Global lock prevents concurrent tests
3. **Backend Selection**: CLI if available, else Python
4. **Test Execution**: 
   - Latency test (10 pings)
   - Download test (15 seconds)
   - Upload test (15 seconds)
5. **Result Processing**: Parse, validate, convert units
6. **Storage**: Update sensors, save lifetime stats
7. **Lock Release**: Allow next instance to test

## Error Handling Strategy

1. **Network Errors**: 3 retries with exponential backoff
2. **CLI Failures**: Fallback to Python backend
3. **Parsing Errors**: Log and return last known good
4. **Timeout**: Configurable, defaults to 2 minutes
5. **Circuit Break**: Stop after 10 consecutive failures

## Future Considerations

1. **Test History**: Store last N results for trending
2. **Statistics**: Min/max/average over time periods
3. **Scheduling**: More complex schedules (peak/off-peak)
4. **Bandwidth Limiting**: Optional throughput caps
5. **Multi-Server**: Test multiple servers in sequence