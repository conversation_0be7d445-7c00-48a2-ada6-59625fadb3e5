# Instructions for Creating a Home Assistant Custom Component with LibreSpeed Integration

## Objective
Create a custom Home Assistant component that integrates LibreSpeed for network speed testing with scheduling, data persistence, and UI configuration capabilities.

## Research Phase
1. **Investigate LibreSpeed implementation options:**
   - Review https://github.com/librespeed/speedtest-cli for CLI capabilities
   - Review https://github.com/librespeed/speedtest for core functionality
   - Determine the optimal integration method:
     - Option A: Use the binary CLI client with subprocess calls
     - Option B: Find or create Python bindings/wrapper
     - Option C: Direct API implementation if available
   - Evaluate pros/cons of each approach considering Home Assistant's architecture

2. **Analyze integration requirements:**
   - Compatibility with Home Assistant's async architecture
   - Resource usage and performance implications
   - Cross-platform compatibility (ensure it works on various HA installation types)

## Required Features

### Core Functionality
- **Speed Test Execution:** Run LibreSpeed tests to measure upload speed, download speed, and latency
- **Data Persistence:** Store all test results in Home Assistant's database
- **Latest Results Display:** Show the most recent test results as sensor entities

### Configuration Features
- **Scheduled Runs:** 
  - Allow users to configure automatic test schedules
  - Support multiple schedule patterns (hourly, daily, weekly, custom cron)
  - Configurable through the UI

- **Server Management:**
  - Retrieve and display available servers from LibreSpeed's server list
  - Allow users to select preferred servers from the list
  - Support custom server specification (user-provided server URLs)
  - Save server preferences in component configuration

### User Interface Requirements
- **Configuration Flow:**
  - Initial setup through Home Assistant's config flow
  - Options flow for modifying settings after initial setup
  
- **Configurable Parameters:**
  - Test schedule (with time/frequency options)
  - Server selection (dropdown of available servers)
  - Custom server URL input field
  - Test timeout settings
  - Enable/disable automatic testing

### Data Entities
Create the following sensor entities:
- `sensor.librespeed_download_speed`
- `sensor.librespeed_upload_speed`
- `sensor.librespeed_latency`
- `sensor.librespeed_last_test_time`
- `sensor.librespeed_server_name`

## Development Plan
1. Research and select implementation method
2. Create proof of concept for LibreSpeed integration
3. Develop Home Assistant component structure
4. Implement core speed test functionality
5. Add scheduling capabilities
6. Create configuration flow and options flow
7. Implement data persistence and sensor entities
8. Add server selection and custom server features
9. Test across different Home Assistant installation types
10. Document setup and configuration process

## Success Criteria
- Component successfully runs speed tests on schedule
- All test results are stored in the database with timestamps
- Users can configure all settings through the UI
- Component properly handles errors and network failures
- Server selection works with both predefined and custom servers

Return with a detailed implementation plan after completing the research phase, including specific technical recommendations for the best approach to integrate LibreSpeed with Home Assistant.
