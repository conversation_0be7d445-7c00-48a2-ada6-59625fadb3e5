# LibreSpeed for Home Assistant

[![Integration Quality Scale](https://img.shields.io/badge/Quality%20Scale-Gold-gold)](QUALITY_SCALE_ASSESSMENT.md)

A Home Assistant custom component that integrates LibreSpeed for network speed testing with scheduling, data persistence, and UI configuration capabilities.

## Overview

LibreSpeed is a free and open-source, self-hosted speed test application. This integration brings LibreSpeed's privacy-respecting speed testing to Home Assistant, allowing you to monitor your internet connection performance over time without sharing data with third parties.

## Features

- **Speed Test Execution**: Run LibreSpeed tests to measure download speed, upload speed, and latency
- **Automatic Scheduling**: Configure automatic speed tests at regular intervals
- **Server Selection**: Choose from available LibreSpeed servers or use custom servers
- **Multiple Backends**: Choose between native Python implementation or official CLI for better performance
- **Data Persistence**: All test results are stored in Home Assistant's database
- **SSL Certificate Options**: Skip certificate verification for self-signed certificates

## Installation

### HACS (Recommended)

1. Add this repository to HACS as a custom repository
2. Search for "LibreSpeed" in HACS
3. Install the integration
4. Restart Home Assistant

### Manual Installation

1. Copy the `custom_components/librespeed` folder to your Home Assistant's `custom_components` directory
2. Restart Home Assistant

## Configuration

### Initial Setup

1. Go to **Settings** → **Devices & Services**
2. Click **+ Add Integration**
3. Search for **LibreSpeed**
4. Follow the configuration wizard with the parameters described below
5. Click **Submit** to complete setup

### Installation Parameters

During initial setup, you'll configure these parameters:

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| **Backend Type** | Select | Yes | Native Python | Processing backend for speed tests:<br>• **Native Python**: Pure Python implementation, suitable for most connections<br>• **Official CLI**: Binary CLI for high-speed connections (>500 Mbps), auto-downloaded |
| **Server Selection** | Select | Yes | Automatic | How to select the test server:<br>• **Automatic**: Auto-detect best server based on latency<br>• **Select Server**: Choose from list of available servers<br>• **Custom Server**: Use your own LibreSpeed instance |
| **Server ID** | Dropdown | If "Select Server" | None | Choose from list of available LibreSpeed servers worldwide |
| **Custom Server URL** | URL | If "Custom Server" | None | Full URL to your LibreSpeed server (e.g., `https://speedtest.example.com`) |
| **Enable Automatic Updates** | Boolean | Yes | True | Run tests automatically on schedule |
| **Update Interval** | Number | If auto enabled | 60 | Minutes between automatic tests (15-1440) |
| **Skip Certificate Verification** | Boolean | No | False | Skip SSL verification for self-signed certificates (CLI backend only) ⚠️ |

### Configuration Options

After initial setup, you can modify these parameters through the integration options:

| Parameter | Type | Modifiable | Range/Options | Description |
|-----------|------|------------|---------------|-------------|
| **Backend Type** | Select | Yes | Native Python / Official CLI | Switch between backends without reinstalling |
| **Server Selection Mode** | Select | Yes | Automatic / Select / Custom | Change server selection method |
| **Server ID** | Dropdown | Yes | Server list | Pick different server from list |
| **Custom Server URL** | URL | Yes | Valid URL | Update custom server address |
| **Enable Automatic Updates** | Toggle | Yes | On/Off | Enable or disable scheduled tests |
| **Update Interval** | Slider | Yes | 15-1440 minutes | Adjust test frequency:<br>• 15-30 min: Frequent monitoring<br>• 60 min: Hourly (default)<br>• 180-360 min: Regular checks<br>• 720-1440 min: Daily monitoring |
| **Skip Certificate Verification** | Toggle | Yes | On/Off | Toggle SSL verification (CLI only) |

#### Modifying Configuration

1. Go to **Settings** → **Devices & Services**
2. Find the LibreSpeed integration
3. Click **Configure**
4. Modify desired parameters
5. Click **Submit** to save changes

**Note**: Changes take effect on the next speed test. Running tests are not interrupted.

## Entities

The integration creates the following entities:

### Sensors
- `sensor.librespeed_download_speed` - Download speed in Mbps
- `sensor.librespeed_upload_speed` - Upload speed in Mbps  
- `sensor.librespeed_ping` - Latency in milliseconds
- `sensor.librespeed_jitter` - Connection jitter in milliseconds
- `sensor.librespeed_packet_loss` - Packet loss percentage
- `sensor.librespeed_downloaded_data` - Data downloaded during last test (MB)
- `sensor.librespeed_uploaded_data` - Data uploaded during last test (MB)
- `sensor.librespeed_server_name` - Name of the server used
- `sensor.librespeed_server_location` - Location of the server used
- `sensor.librespeed_last_test_time` - Timestamp of the last test

### Button
- `button.librespeed_run_speed_test` - Manually trigger a speed test

### Binary Sensor
- `binary_sensor.librespeed_speed_test_running` - Indicates if a test is currently running

## Usage Examples

### Automation Example

Trigger a speed test when internet connectivity is restored:

```yaml
automation:
  - alias: "Speed Test on Internet Restore"
    trigger:
      - platform: state
        entity_id: binary_sensor.internet_connection
        to: "on"
        from: "off"
        for: "00:02:00"
    action:
      - service: button.press
        target:
          entity_id: button.librespeed_run_speed_test
```

### Dashboard Card Example

```yaml
type: vertical-stack
cards:
  - type: entities
    title: Internet Speed
    entities:
      - entity: sensor.librespeed_download_speed
        name: Download
      - entity: sensor.librespeed_upload_speed
        name: Upload
      - entity: sensor.librespeed_ping
        name: Latency
      - entity: button.librespeed_run_speed_test
        name: Run Test
  - type: history-graph
    title: Speed History
    entities:
      - sensor.librespeed_download_speed
      - sensor.librespeed_upload_speed
```

## Custom Server Setup

If you're running your own LibreSpeed server:

1. Select "Custom Server" during configuration
2. Enter your server URL (e.g., `https://speedtest.example.com`)
3. Enable "Skip Certificate Verification" if using self-signed certificates

### Supported Server Types
- Standard LibreSpeed PHP backend
- LibreSpeed Rust backend
- Any LibreSpeed-compatible server

## Troubleshooting

### Common Issues

#### Speed test button is disabled
**Symptom**: The "Run Speed Test" button appears grayed out or unavailable.

**Cause**: A speed test is currently running. The integration prevents concurrent tests to ensure accurate results.

**Solution**: 
- Wait for the current test to complete (check `binary_sensor.librespeed_speed_test_running`)
- Check logs for any errors if the test seems stuck
- Restart the integration if the test has been running for more than 5 minutes

#### Low speed results with native backend
**Symptom**: Speed test results are significantly lower than expected (e.g., 50-100 Mbps when expecting 500+ Mbps).

**Solutions**:
1. **Switch to CLI backend** (recommended for high-speed connections):
   - Navigate to Settings → Devices & Services → LibreSpeed → Configure
   - Change Backend Type to "Official CLI"
   - Save and wait for automatic restart
   
2. **Select a different server**:
   - Some servers may be overloaded or geographically distant
   - Try "Automatic" selection or manually choose a closer server

3. **Check network conditions**:
   - Ensure no other devices are consuming bandwidth
   - Test at different times of day

#### Custom server not working
**Symptom**: Tests fail when using a custom LibreSpeed server.

**Diagnostic steps**:
1. **Verify server URL**:
   - Ensure URL is complete (e.g., `https://speedtest.example.com`)
   - Test the URL in a browser - you should see the LibreSpeed interface
   
2. **SSL Certificate issues**:
   - If using self-signed certificates, enable "Skip Certificate Verification"
   - Check logs for SSL-related errors
   
3. **Server compatibility**:
   - Ensure server is running LibreSpeed (PHP or Rust backend)
   - Check server logs for any access errors

#### Tests not running automatically
**Symptom**: Scheduled speed tests are not executing at the configured interval.

**Solutions**:
1. **Verify configuration**:
   - Check that "Automatic Updates" is enabled
   - Confirm update interval is set (minimum 30 minutes)
   
2. **Check Home Assistant logs**:
   ```
   grep librespeed home-assistant.log
   ```
   
3. **Review automation conflicts**:
   - Ensure no other automations are interfering
   - Check if Home Assistant is in maintenance mode

### Error Messages and Solutions

#### "Speed test timed out after multiple attempts"
**Cause**: Network connectivity issues or server unavailability.

**Solutions**:
- Check internet connectivity
- Try a different server
- Increase timeout in advanced settings (if available)
- The integration automatically retries 3 times with exponential backoff

#### "Network error: Connection refused"
**Cause**: Firewall blocking connections or server is down.

**Solutions**:
- Check firewall rules for outbound HTTPS (port 443)
- Verify the server is operational
- Try the automatic server selection

#### "SSL: CERTIFICATE_VERIFY_FAILED"
**Cause**: Custom server using self-signed or expired certificate.

**Solutions**:
- Enable "Skip Certificate Verification" in configuration
- Update server certificate
- Use a Let's Encrypt certificate on your server

#### "Speed test already in progress"
**Cause**: Previous test didn't complete properly.

**Solutions**:
- Wait 2-3 minutes for timeout
- Restart the integration if stuck
- Check logs for underlying errors

### Advanced Debugging

#### Enable debug logging
Add to `configuration.yaml`:
```yaml
logger:
  default: info
  logs:
    custom_components.librespeed: debug
```

#### Check integration state
In Developer Tools → States, search for `librespeed` entities:
- `binary_sensor.librespeed_speed_test_running` - Should be `off` when idle
- `sensor.librespeed_last_test_time` - Shows last successful test

#### Manual testing
Test the CLI backend manually:
```bash
# From Home Assistant container/environment
./custom_components/librespeed/speedtest-cli --json
```

### Performance Optimization

#### For connections > 500 Mbps
- Use CLI backend for best performance
- Ensure Home Assistant has sufficient CPU resources
- Consider running tests during off-peak hours

#### Reducing resource usage
- Increase update interval (e.g., every 6-12 hours)
- Disable automatic updates and use automations for specific times
- Use server selection instead of automatic to avoid latency tests

## Detailed Entity Documentation

### Primary Sensors (Always Visible)

#### Download Speed (`sensor.librespeed_download_speed`)
- **Type**: Measurement sensor
- **Unit**: Mbps (Megabits per second)
- **Update**: After each speed test
- **Attributes**:
  - `bytes_received`: Total bytes downloaded during test
  - `test_duration`: Duration of download test in seconds
- **Use Cases**: Monitor internet performance, trigger automations on low speeds

#### Upload Speed (`sensor.librespeed_upload_speed`)
- **Type**: Measurement sensor  
- **Unit**: Mbps (Megabits per second)
- **Update**: After each speed test
- **Attributes**:
  - `bytes_sent`: Total bytes uploaded during test
  - `test_duration`: Duration of upload test in seconds
- **Use Cases**: Monitor upload capacity for backups, streaming

#### Ping (`sensor.librespeed_ping`)
- **Type**: Measurement sensor
- **Unit**: ms (milliseconds)
- **Update**: After each speed test
- **Description**: Round-trip time to speed test server
- **Use Cases**: Gaming performance monitoring, VoIP quality checks

### Diagnostic Sensors (Hidden by Default)

#### Jitter (`sensor.librespeed_jitter`)
- **Type**: Diagnostic sensor
- **Unit**: ms (milliseconds)
- **Description**: Variation in ping times
- **Use Cases**: VoIP/video call quality monitoring

#### Downloaded Data (`sensor.librespeed_downloaded_data`)
- **Type**: Diagnostic sensor
- **Unit**: MB (Megabytes)
- **Description**: Amount of data downloaded during last test
- **Use Cases**: Monitor test data usage

#### Uploaded Data (`sensor.librespeed_uploaded_data`)
- **Type**: Diagnostic sensor
- **Unit**: MB (Megabytes)
- **Description**: Amount of data uploaded during last test
- **Use Cases**: Monitor test data usage

#### Server Name (`sensor.librespeed_server_name`)
- **Type**: Diagnostic sensor
- **Attributes**:
  - `server_id`: Unique server identifier
  - `server_sponsor`: Server sponsor/owner
  - `server_url`: Server URL
- **Use Cases**: Track which server was used for testing

#### Last Test Time (`sensor.librespeed_last_test_time`)
- **Type**: Timestamp sensor
- **Description**: When the last speed test was performed
- **Use Cases**: Track test history, create time-based automations

### Controls

#### Run Speed Test Button (`button.librespeed_run_speed_test`)
- **Type**: Button entity
- **Action**: Triggers immediate speed test
- **Availability**: Disabled during active test
- **Use Cases**: Manual testing, automation triggers

### Status Indicators

#### Speed Test Running (`binary_sensor.librespeed_speed_test_running`)
- **Type**: Binary sensor (diagnostic)
- **States**: 
  - `on`: Test currently running
  - `off`: No test active
- **Use Cases**: Prevent concurrent tests, status displays

## Automation Blueprints

### Blueprint: Daily Speed Test Report

Save this as `librespeed_daily_report.yaml`:

```yaml
blueprint:
  name: LibreSpeed Daily Report
  description: Send daily speed test report via notification
  domain: automation
  input:
    test_time:
      name: Test Time
      description: When to run the daily test
      selector:
        time:
      default: "03:00:00"
    notify_device:
      name: Notification Device
      description: Device to send the report to
      selector:
        device:
          integration: mobile_app
    speed_threshold:
      name: Minimum Download Speed
      description: Alert if speed is below this value (Mbps)
      selector:
        number:
          min: 1
          max: 1000
          unit_of_measurement: Mbps
      default: 50

trigger:
  - platform: time
    at: !input test_time

action:
  - service: button.press
    target:
      entity_id: button.librespeed_run_speed_test
  
  - wait_for_trigger:
      - platform: state
        entity_id: binary_sensor.librespeed_speed_test_running
        from: "on"
        to: "off"
    timeout: "00:05:00"
  
  - variables:
      download: "{{ states('sensor.librespeed_download_speed') | float }}"
      upload: "{{ states('sensor.librespeed_upload_speed') | float }}"
      ping: "{{ states('sensor.librespeed_ping') | float }}"
      threshold: !input speed_threshold
  
  - service: notify.mobile_app_{{ device_id(!input notify_device) }}
    data:
      title: "Daily Speed Test Report"
      message: >
        📊 Speed Test Results:
        Download: {{ download }} Mbps {{ '⚠️' if download < threshold else '✅' }}
        Upload: {{ upload }} Mbps
        Ping: {{ ping }} ms
        Server: {{ states('sensor.librespeed_server_name') }}
      data:
        tag: "speed_test_report"
        group: "speed_test"
```

### Blueprint: Low Speed Alert

Save this as `librespeed_low_speed_alert.yaml`:

```yaml
blueprint:
  name: LibreSpeed Low Speed Alert
  description: Alert when internet speed drops below threshold
  domain: automation
  input:
    download_threshold:
      name: Download Speed Threshold
      description: Alert when download speed is below (Mbps)
      selector:
        number:
          min: 1
          max: 1000
          unit_of_measurement: Mbps
      default: 50
    upload_threshold:
      name: Upload Speed Threshold
      description: Alert when upload speed is below (Mbps)
      selector:
        number:
          min: 1
          max: 500
          unit_of_measurement: Mbps
      default: 10
    notify_device:
      name: Notification Device
      selector:
        device:
          integration: mobile_app

trigger:
  - platform: numeric_state
    entity_id: sensor.librespeed_download_speed
    below: !input download_threshold
  - platform: numeric_state
    entity_id: sensor.librespeed_upload_speed
    below: !input upload_threshold

action:
  - service: notify.mobile_app_{{ device_id(!input notify_device) }}
    data:
      title: "⚠️ Slow Internet Speed Detected"
      message: >
        {% if trigger.entity_id == 'sensor.librespeed_download_speed' %}
        Download speed is {{ states('sensor.librespeed_download_speed') }} Mbps
        (below {{ download_threshold }} Mbps threshold)
        {% else %}
        Upload speed is {{ states('sensor.librespeed_upload_speed') }} Mbps
        (below {{ upload_threshold }} Mbps threshold)
        {% endif %}
      data:
        tag: "speed_alert"
        importance: high
        channel: alerts
```

## Advanced Use Cases

### Network Quality Monitoring Dashboard

Create a comprehensive dashboard for monitoring network quality:

```yaml
type: vertical-stack
cards:
  - type: custom:mini-graph-card
    name: Internet Speed History
    entities:
      - entity: sensor.librespeed_download_speed
        name: Download
        color: blue
      - entity: sensor.librespeed_upload_speed
        name: Upload
        color: green
    hours_to_show: 24
    points_per_hour: 4
    line_width: 3
    font_size: 75
    
  - type: horizontal-stack
    cards:
      - type: gauge
        entity: sensor.librespeed_download_speed
        name: Download
        max: 1000
        severity:
          green: 100
          yellow: 50
          red: 0
          
      - type: gauge
        entity: sensor.librespeed_upload_speed
        name: Upload
        max: 500
        severity:
          green: 50
          yellow: 25
          red: 0
          
      - type: gauge
        entity: sensor.librespeed_ping
        name: Latency
        max: 100
        severity:
          green: 0
          yellow: 50
          red: 100
          
  - type: entities
    title: Test Details
    entities:
      - entity: sensor.librespeed_jitter
        name: Jitter
      - entity: sensor.librespeed_server_name
        name: Server
      - entity: sensor.librespeed_last_test_time
        name: Last Test
      - entity: binary_sensor.librespeed_speed_test_running
        name: Test Status
      - entity: button.librespeed_run_speed_test
        name: Run Test Now
```

### ISP Performance Tracking

Track ISP performance against advertised speeds:

```yaml
sensor:
  - platform: template
    sensors:
      internet_performance_percentage:
        friendly_name: "ISP Performance"
        unit_of_measurement: "%"
        value_template: >
          {% set advertised = 500 %}  # Your advertised speed in Mbps
          {% set actual = states('sensor.librespeed_download_speed') | float %}
          {{ ((actual / advertised) * 100) | round(1) }}
        icon_template: >
          {% set perf = states('sensor.internet_performance_percentage') | float %}
          {% if perf >= 90 %}
            mdi:check-circle
          {% elif perf >= 70 %}
            mdi:alert-circle
          {% else %}
            mdi:close-circle
          {% endif %}

  - platform: statistics
    name: "Average Download Speed"
    entity_id: sensor.librespeed_download_speed
    state_characteristic: mean
    max_age:
      days: 7
```

### Integration with Other Services

#### Discord Notification Example

```yaml
automation:
  - alias: "Speed Test Discord Report"
    trigger:
      - platform: state
        entity_id: binary_sensor.librespeed_speed_test_running
        from: "on"
        to: "off"
    action:
      - service: notify.discord
        data:
          message: |
            **Speed Test Results**
            :arrow_down: Download: {{ states('sensor.librespeed_download_speed') }} Mbps
            :arrow_up: Upload: {{ states('sensor.librespeed_upload_speed') }} Mbps
            :stopwatch: Ping: {{ states('sensor.librespeed_ping') }} ms
            :satellite: Server: {{ states('sensor.librespeed_server_name') }}
```

## Compatible Servers

The integration is compatible with:

- All official LibreSpeed servers (100+ worldwide)
- Self-hosted LibreSpeed instances (PHP backend)
- LibreSpeed Rust backend servers
- Any server implementing the LibreSpeed protocol

### Setting Up Your Own Server

1. **Using Docker (Recommended)**:
```bash
docker run -d --name librespeed \
  -p 80:80 \
  adolfintel/speedtest
```

2. **Manual Installation**: Follow the [LibreSpeed installation guide](https://github.com/librespeed/speedtest)

3. **Configure in Home Assistant**:
   - Select "Custom Server" in configuration
   - Enter your server URL
   - Enable certificate skip if using self-signed SSL

## Removal Instructions

### Removing the Integration

1. **Via UI**:
   - Go to **Settings** → **Devices & Services**
   - Find the LibreSpeed integration
   - Click the three dots menu (⋮)
   - Select **Delete**
   - Confirm the removal

2. **What gets removed**:
   - All LibreSpeed entities
   - The LibreSpeed device
   - Scheduled speed tests
   - Integration configuration

3. **What remains**:
   - Historical data in the database (can be purged separately)
   - Downloaded CLI binary (if using CLI backend)

### Uninstalling the Integration Files

**If installed via HACS**:
1. Go to HACS → Integrations
2. Search for LibreSpeed
3. Click the three dots menu
4. Select "Remove"
5. Restart Home Assistant

**If installed manually**:
1. Delete the folder: `custom_components/librespeed`
2. Restart Home Assistant

### Cleaning Up Remaining Data

**Remove historical data**:
```sql
-- Use with caution in Developer Tools → Services → recorder.purge_entities
service: recorder.purge_entities
data:
  entity_id:
    - sensor.librespeed_download_speed
    - sensor.librespeed_upload_speed
    # ... list all LibreSpeed entities
```

**Remove CLI binary** (if CLI backend was used):
```bash
rm -rf custom_components/librespeed/bin/
```

## Support

For issues and feature requests, please use the [GitHub issue tracker](https://github.com/yourusername/librespeed-ha/issues).

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Credits

- [LibreSpeed](https://github.com/librespeed/speedtest) - The open source speed test
- [LibreSpeed CLI](https://github.com/librespeed/speedtest-cli) - Official command line client