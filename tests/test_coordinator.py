"""Test LibreSpeed coordinator functionality."""
from datetime import timedelta
from unittest.mock import AsyncMock, Mock, patch
import asyncio
import aiohttp

import pytest
from homeassistant.core import HomeAssistant
from homeassistant.exceptions import ConfigEntryNotReady
from homeassistant.helpers.update_coordinator import UpdateFailed

from custom_components.librespeed import LibreSpeedDataUpdateCoordinator
from custom_components.librespeed.const import DOMAIN


@pytest.fixture
def mock_client():
    """Create a mock LibreSpeed client."""
    client = Mock()
    client.run_speed_test = AsyncMock(return_value={
        "download": 100.0,
        "upload": 50.0,
        "ping": 10.0,
        "jitter": 2.0,
        "server": {"name": "Test Server", "location": "Test Location"},
        "timestamp": "2024-01-01T12:00:00Z",
        "bytes_sent": 10485760,
        "bytes_received": 20971520,
    })
    return client




async def test_coordinator_initialization(hass: HomeAssistant, mock_client):
    """Test coordinator initialization with various configurations."""
    # Test with auto-update enabled
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=1,
        custom_server=None,
        scan_interval=60,
        auto_update=True,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    assert coordinator.server_id == 1
    assert coordinator.custom_server is None
    assert coordinator.auto_update is True
    assert coordinator.update_interval == timedelta(minutes=60)
    assert coordinator.is_running is False
    assert coordinator.backend_type == "native"
    
    # Test with auto-update disabled
    coordinator_manual = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=None,
        custom_server="https://custom.server.com",
        scan_interval=30,
        auto_update=False,
        skip_cert_verify=True,
        backend_type="cli",
    )
    
    assert coordinator_manual.custom_server == "https://custom.server.com"
    assert coordinator_manual.auto_update is False
    assert coordinator_manual.update_interval is None  # No auto-update
    assert coordinator_manual.skip_cert_verify is True
    assert coordinator_manual.backend_type == "cli"


async def test_coordinator_update_success(hass: HomeAssistant, mock_client):
    """Test successful coordinator update."""
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=1,
        custom_server=None,
        scan_interval=60,
        auto_update=True,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    # Perform update
    result = await coordinator._async_update_data()
    
    # Verify results
    assert result["download"] == 100.0
    assert result["upload"] == 50.0
    assert result["ping"] == 10.0
    assert coordinator.is_running is False  # Should be reset after completion
    
    # Verify client was called correctly (native backend)
    mock_client.run_speed_test.assert_called_once_with(
        server_id=1,
        custom_server_url=None,
        timeout=240,
    )


async def test_coordinator_update_with_cli_backend(hass: HomeAssistant, mock_client):
    """Test coordinator update with CLI backend."""
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=None,
        custom_server="https://test.server.com",
        scan_interval=60,
        auto_update=True,
        skip_cert_verify=True,
        backend_type="cli",
    )
    
    # Perform update
    result = await coordinator._async_update_data()
    
    # Verify client was called with CLI-specific parameters
    mock_client.run_speed_test.assert_called_once_with(
        server_id=None,
        custom_server="https://test.server.com",
        skip_cert_verify=True,
        timeout=240,
    )
    
    assert result["download"] == 100.0


async def test_coordinator_retry_logic(hass: HomeAssistant, mock_client):
    """Test coordinator retry logic on failures."""
    # Configure client to fail twice, then succeed
    mock_client.run_speed_test = AsyncMock(
        side_effect=[
            aiohttp.ClientError("Network error"),
            asyncio.TimeoutError(),
            {"download": 75.0, "upload": 25.0, "ping": 15.0,
             "server": {"name": "Server"}, "timestamp": "2024-01-01T12:00:00Z",
             "bytes_sent": 0, "bytes_received": 0}
        ]
    )
    
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=1,
        custom_server=None,
        scan_interval=60,
        auto_update=True,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    # Perform update - should retry and eventually succeed
    with patch("custom_components.librespeed.asyncio.sleep"):  # Skip delay
        result = await coordinator._async_update_data()
    
    # Should have been called 3 times (2 failures + 1 success)
    assert mock_client.run_speed_test.call_count == 3
    assert result["download"] == 75.0


async def test_coordinator_max_retries_exceeded(hass: HomeAssistant, mock_client):
    """Test coordinator when max retries are exceeded."""
    # Configure client to always fail
    mock_client.run_speed_test = AsyncMock(
        side_effect=aiohttp.ClientError("Persistent network error")
    )
    
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=1,
        custom_server=None,
        scan_interval=60,
        auto_update=True,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    # Perform update - should raise UpdateFailed after max retries
    with patch("custom_components.librespeed.asyncio.sleep"):  # Skip delay
        with pytest.raises(UpdateFailed, match="Network error after multiple attempts"):
            await coordinator._async_update_data()
    
    # Should have been called MAX_RETRIES times
    assert mock_client.run_speed_test.call_count == 3  # MAX_RETRIES = 3
    assert coordinator.is_running is False  # Should be reset even on failure


async def test_coordinator_concurrent_test_prevention(hass: HomeAssistant, mock_client):
    """Test that coordinator prevents concurrent speed tests."""
    # Configure client with a delay to simulate long-running test
    async def slow_test(*args, **kwargs):
        await asyncio.sleep(0.5)
        return {
            "download": 100.0, "upload": 50.0, "ping": 10.0,
            "server": {"name": "Server"}, "timestamp": "2024-01-01T12:00:00Z",
            "bytes_sent": 0, "bytes_received": 0
        }
    
    mock_client.run_speed_test = slow_test
    
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=1,
        custom_server=None,
        scan_interval=60,
        auto_update=True,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    # Start first test
    task1 = asyncio.create_task(coordinator._async_update_data())
    
    # Try to start second test immediately
    await asyncio.sleep(0.1)  # Let first test start
    
    # Second test should wait for first to complete (new behavior with global lock)
    task2 = asyncio.create_task(coordinator._async_update_data())
    
    # Both tests should complete successfully
    result1 = await task1
    result2 = await task2
    
    # Both should have results (second waits for first)
    assert result1["download"] == 100.0
    assert result2["download"] == 100.0


async def test_coordinator_manual_speed_test(hass: HomeAssistant, mock_client):
    """Test manual speed test execution."""
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=2,
        custom_server=None,
        scan_interval=60,
        auto_update=False,  # Manual mode
        skip_cert_verify=False,
        backend_type="native",
    )
    
    # Run manual test
    await coordinator.async_run_speedtest()
    
    # Verify test was executed
    mock_client.run_speed_test.assert_called_once_with(
        server_id=2,
        custom_server_url=None,
        timeout=240,
    )
    
    # Verify data was updated
    assert coordinator.data["download"] == 100.0
    assert coordinator.data["upload"] == 50.0


async def test_coordinator_first_refresh_skip(hass: HomeAssistant, mock_client):
    """Test that coordinator runs tests even in manual mode when called directly."""
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=1,
        custom_server=None,
        scan_interval=60,
        auto_update=False,  # Manual mode
        skip_cert_verify=False,
        backend_type="native",
    )
    
    # Even in manual mode, _async_update_data should run the test
    result = await coordinator._async_update_data()
    
    # Should run the test and return results
    assert result["download"] == 100.0
    assert mock_client.run_speed_test.call_count == 1
    
    # Second refresh should also work
    result = await coordinator._async_update_data()
    assert result["download"] == 100.0
    assert mock_client.run_speed_test.call_count == 2


async def test_coordinator_is_running_flag(hass: HomeAssistant, mock_client):
    """Test that is_running flag is properly managed."""
    # Add delay to test to verify is_running flag
    async def test_with_delay(*args, **kwargs):
        # Check that is_running is True during test
        assert coordinator.is_running is True
        await asyncio.sleep(0.1)
        return {
            "download": 100.0, "upload": 50.0, "ping": 10.0,
            "server": {"name": "Server"}, "timestamp": "2024-01-01T12:00:00Z",
            "bytes_sent": 0, "bytes_received": 0
        }
    
    mock_client.run_speed_test = test_with_delay
    
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=1,
        custom_server=None,
        scan_interval=60,
        auto_update=True,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    # Verify is_running is False initially
    assert coordinator.is_running is False
    
    # Run test
    await coordinator._async_update_data()
    
    # Verify is_running is reset to False after test
    assert coordinator.is_running is False