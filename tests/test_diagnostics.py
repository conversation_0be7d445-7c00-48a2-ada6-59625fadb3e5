"""Test LibreSpeed diagnostics."""
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, Mock, patch

import pytest
from homeassistant.core import HomeAssistant

from custom_components.librespeed.const import DOMAIN


@pytest.fixture
def mock_coordinator():
    """Create a mock coordinator with test data."""
    coordinator = Mock()
    coordinator.data = {
        "download": 100.5,
        "upload": 50.2,
        "ping": 10.1,
        "jitter": 2.5,
        "server_name": "Test Server",
        "server_location": "Test Location",
        "timestamp": datetime(2024, 1, 1, 12, 0, 0),
        "bytes_sent": 62914560,
        "bytes_received": 125829120,
    }
    coordinator.backend_type = "native"
    coordinator.auto_update = True
    coordinator.update_interval = timedelta(minutes=60)
    coordinator.is_running = False
    coordinator.last_update_success = True
    coordinator.last_exception = None
    coordinator.hass = Mock()
    coordinator.hass.config.platform = "linux"
    coordinator.hass.config.python_version = "3.11.0"
    
    # Mock client
    coordinator.client = Mock()
    coordinator.client.get_servers = AsyncMock(return_value=[
        {"id": 1, "name": "Server 1", "location": "Location 1", "country": "US"},
        {"id": 2, "name": "Server 2", "location": "Location 2", "country": "UK"},
    ])
    
    # Add config_entry mock
    coordinator.config_entry = Mock()
    coordinator.config_entry.entry_id = "test_entry"
    coordinator.entry_title = "LibreSpeed Test"
    
    return coordinator


@pytest.fixture
def mock_config_entry():
    """Create a mock config entry."""
    entry = Mock()
    entry.entry_id = "test_entry_id"
    entry.version = 1
    entry.domain = DOMAIN
    entry.title = "LibreSpeed"
    entry.source = "user"
    entry.data = {
        "server_id": None,
        "custom_server": "https://test.example.com",
        "auto_update": True,
        "scan_interval": 60,
        "backend_type": "native",
        "skip_cert_verify": False,
    }
    entry.options = {}
    return entry


async def test_diagnostics_data(hass: HomeAssistant, mock_coordinator, mock_config_entry):
    """Test diagnostics data generation."""
    from custom_components.librespeed.diagnostics import async_get_config_entry_diagnostics
    from custom_components.librespeed import LibreSpeedRuntimeData
    
    # Set up runtime_data
    runtime_data = LibreSpeedRuntimeData(coordinator=mock_coordinator, session=None)
    mock_config_entry.runtime_data = runtime_data
    
    diagnostics = await async_get_config_entry_diagnostics(hass, mock_config_entry)
    
    # Check entry data
    assert diagnostics["entry"]["entry_id"] == "test_entry_id"
    assert diagnostics["entry"]["title"] == "LibreSpeed"
    assert diagnostics["entry"]["domain"] == DOMAIN
    
    # Check coordinator data
    assert diagnostics["coordinator"]["backend_type"] == "native"
    assert diagnostics["coordinator"]["auto_update"] is True
    assert diagnostics["coordinator"]["scan_interval"] == 3600  # 60 minutes in seconds
    assert diagnostics["coordinator"]["is_running"] is False
    assert diagnostics["coordinator"]["last_update_success"] is True
    
    # Check last test data
    assert diagnostics["last_test"]["download_speed"] == 100.5
    assert diagnostics["last_test"]["upload_speed"] == 50.2
    assert diagnostics["last_test"]["ping"] == 10.1
    
    # Check platform info
    assert diagnostics["platform_info"]["platform"] == "linux"
    assert diagnostics["platform_info"]["python_version"] == "3.11.0"


async def test_diagnostics_redaction(hass: HomeAssistant, mock_coordinator, mock_config_entry):
    """Test sensitive data is redacted."""
    from custom_components.librespeed.diagnostics import async_get_config_entry_diagnostics
    from custom_components.librespeed import LibreSpeedRuntimeData
    
    # Set up runtime_data
    runtime_data = LibreSpeedRuntimeData(coordinator=mock_coordinator, session=None)
    mock_config_entry.runtime_data = runtime_data
    
    diagnostics = await async_get_config_entry_diagnostics(hass, mock_config_entry)
    
    # Check that custom_server is redacted
    assert "**REDACTED**" in str(diagnostics["entry"]["data"])
    assert "https://test.example.com" not in str(diagnostics)


async def test_diagnostics_cli_backend(hass: HomeAssistant, mock_coordinator, mock_config_entry):
    """Test diagnostics with CLI backend."""
    from custom_components.librespeed.diagnostics import async_get_config_entry_diagnostics
    from custom_components.librespeed import LibreSpeedRuntimeData
    
    mock_coordinator.backend_type = "cli"
    mock_coordinator.client.cli_path = "/path/to/cli"
    
    # Set up runtime_data
    runtime_data = LibreSpeedRuntimeData(coordinator=mock_coordinator, session=None)
    mock_config_entry.runtime_data = runtime_data
    
    diagnostics = await async_get_config_entry_diagnostics(hass, mock_config_entry)
    
    assert diagnostics["cli_info"]["backend"] == "CLI"
    assert diagnostics["cli_info"]["cli_available"] is True
    assert diagnostics["cli_info"]["cli_path"] == "/path/to/cli"


async def test_diagnostics_no_data(hass: HomeAssistant, mock_config_entry):
    """Test diagnostics when no test data is available."""
    from custom_components.librespeed.diagnostics import async_get_config_entry_diagnostics
    from custom_components.librespeed import LibreSpeedRuntimeData
    
    coordinator = Mock()
    coordinator.data = None
    coordinator.backend_type = "native"
    coordinator.auto_update = False
    coordinator.update_interval = None
    coordinator.is_running = False
    coordinator.last_update_success = False
    coordinator.last_exception = Exception("Test error")
    coordinator.hass = Mock()
    coordinator.hass.config.platform = "linux"
    coordinator.hass.config.python_version = "3.11.0"
    coordinator.client = Mock()
    coordinator.client.get_servers = AsyncMock(side_effect=Exception("Server error"))
    
    # Set up runtime_data
    runtime_data = LibreSpeedRuntimeData(coordinator=coordinator, session=None)
    mock_config_entry.runtime_data = runtime_data
    
    diagnostics = await async_get_config_entry_diagnostics(hass, mock_config_entry)
    
    assert diagnostics["last_test"] is None
    assert diagnostics["coordinator"]["last_exception"] == "Test error"
    assert diagnostics["server_list_sample"] == "Failed to retrieve server list"


async def test_diagnostics_server_list(hass: HomeAssistant, mock_coordinator, mock_config_entry):
    """Test server list in diagnostics."""
    from custom_components.librespeed.diagnostics import async_get_config_entry_diagnostics
    from custom_components.librespeed import LibreSpeedRuntimeData
    
    # Mock a large server list
    large_server_list = [
        {"id": i, "name": f"Server {i}", "location": f"Location {i}", "country": "US"}
        for i in range(20)
    ]
    mock_coordinator.client.get_servers = AsyncMock(return_value=large_server_list)
    
    # Set up runtime_data
    runtime_data = LibreSpeedRuntimeData(coordinator=mock_coordinator, session=None)
    mock_config_entry.runtime_data = runtime_data
    
    diagnostics = await async_get_config_entry_diagnostics(hass, mock_config_entry)
    
    # Should only include first 10 servers
    assert len(diagnostics["server_list_sample"]) == 10
    assert diagnostics["server_list_sample"][0]["name"] == "Server 0"
    assert diagnostics["server_list_sample"][9]["name"] == "Server 9"