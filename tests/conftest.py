"""Common fixtures for LibreSpeed tests."""
import asyncio
import sys
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch

import pytest

# Add the custom_components directory to the path so Home Assistant can find it
sys.path.insert(0, str(Path(__file__).parent.parent))

from custom_components.librespeed.const import DOMAIN


@pytest.fixture
def mock_setup_entry():
    """Mock setup entry."""
    with patch(
        "custom_components.librespeed.async_setup_entry",
        return_value=True,
    ) as mock_setup:
        yield mock_setup


@pytest.fixture
def mock_librespeed_client():
    """Mock LibreSpeed client."""
    with patch("custom_components.librespeed.config_flow.LibreSpeedClient") as mock_client:
        instance = mock_client.return_value
        instance.get_servers = AsyncMock(return_value=[
            {"id": 1, "name": "Server 1", "location": "Location 1", "server": "https://server1.example.com"},
            {"id": 2, "name": "Server 2", "location": "Location 2", "server": "https://server2.example.com"},
        ])
        instance.run_speed_test = AsyncMock(return_value={
            "download": 100.5,
            "upload": 50.2,
            "ping": 10.1,
            "bytes_received": 125829120,
            "bytes_sent": 62914560,
            "server": {"name": "Server 1", "location": "Location 1"},
            "timestamp": "2024-01-01T12:00:00Z",
        })
        yield mock_client


@pytest.fixture
def mock_get_server_list():
    """Mock get_server_list function."""
    with patch("custom_components.librespeed.config_flow.get_server_list") as mock_func:
        mock_func.return_value = [
            {"id": 1, "name": "Server 1", "location": "Location 1"},
            {"id": 2, "name": "Server 2", "location": "Location 2"},
        ]
        yield mock_func


@pytest.fixture(autouse=True)
def auto_enable_custom_integrations(enable_custom_integrations):
    """Enable custom integrations for testing."""
    yield


@pytest.fixture(autouse=True)
def setup_global_lock(hass):
    """Setup global lock for all tests that need coordinator."""
    # Only setup if hass fixture is being used
    if hass:
        hass.data.setdefault(DOMAIN, {})
        if "test_lock" not in hass.data[DOMAIN]:
            hass.data[DOMAIN]["test_lock"] = asyncio.Lock()
    yield


@pytest.fixture
def create_mock_entry():
    """Create a mock config entry with all required attributes."""
    def _create_entry(**kwargs):
        mock_entry = Mock()
        mock_entry.entry_id = kwargs.get("entry_id", "test_entry_id")
        mock_entry.title = kwargs.get("title", "LibreSpeed Test")
        mock_entry.data = kwargs.get("data", {})
        mock_entry.options = kwargs.get("options", {})
        mock_entry.version = kwargs.get("version", 1)
        mock_entry.domain = kwargs.get("domain", "librespeed")
        mock_entry.source = kwargs.get("source", "user")
        mock_entry.runtime_data = kwargs.get("runtime_data", None)
        mock_entry.async_on_unload = Mock()
        mock_entry.add_update_listener = Mock(return_value=Mock())
        return mock_entry
    return _create_entry