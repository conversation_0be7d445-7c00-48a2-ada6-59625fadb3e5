"""Test that lifetime data persists across reloads."""
from unittest.mock import Mock, AsyncMock, patch
import pytest
from homeassistant.core import HomeAssistant

from custom_components.librespeed import LibreSpeedDataUpdateCoordinator
from custom_components.librespeed.const import (
    ATTR_LIFETIME_DOWNLOAD,
    ATTR_LIFETIME_UPLOAD,
)


async def test_lifetime_data_persistence(hass: HomeAssistant):
    """Test that lifetime data persists through coordinator recreation."""
    # Create first coordinator and simulate some tests
    mock_client1 = Mock()
    mock_client1.run_speed_test = AsyncMock(return_value={
        "download": 100,
        "upload": 50,
        "ping": 10,
        "bytes_received": 10_000_000,  # 10 MB
        "bytes_sent": 5_000_000,  # 5 MB
    })
    
    coordinator1 = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client1,
        server_id=None,
        custom_server=None,
        scan_interval=60,
        auto_update=False,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    # Wait for storage to load (it's async)
    await hass.async_block_till_done()
    
    # Run a speed test
    result1 = await coordinator1._async_update_data()
    assert abs(coordinator1.lifetime_download - 0.010) < 0.000001  # 10 MB = 0.010 GB decimal
    assert abs(coordinator1.lifetime_upload - 0.005) < 0.000001  # 5 MB = 0.005 GB decimal
    
    # Run another test
    result2 = await coordinator1._async_update_data()
    assert abs(coordinator1.lifetime_download - 0.020) < 0.000001  # 20 MB = 0.020 GB decimal
    assert abs(coordinator1.lifetime_upload - 0.010) < 0.000001  # 10 MB = 0.010 GB decimal
    
    # Now create a new coordinator (simulating reload)
    mock_client2 = Mock()
    mock_client2.run_speed_test = AsyncMock(return_value={
        "download": 200,
        "upload": 100,
        "ping": 5,
        "bytes_received": 30_000_000,  # 30 MB
        "bytes_sent": 15_000_000,  # 15 MB
    })
    
    coordinator2 = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client2,
        server_id=None,
        custom_server=None,
        scan_interval=60,
        auto_update=False,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    # Load lifetime data (should be called automatically)
    await coordinator2._async_load_lifetime_data()
    
    # Should have loaded the previous values
    assert abs(coordinator2.lifetime_download - 0.020) < 0.000001  # 20 MB = 0.020 GB decimal
    assert abs(coordinator2.lifetime_upload - 0.010) < 0.000001  # 10 MB = 0.010 GB decimal
    
    # Run a new test with the new coordinator
    result3 = await coordinator2._async_update_data()
    
    # Should have accumulated on top of loaded values
    assert abs(coordinator2.lifetime_download - 0.050) < 0.000001  # 50 MB = 0.050 GB decimal
    assert abs(coordinator2.lifetime_upload - 0.025) < 0.000001  # 25 MB = 0.025 GB decimal
    assert abs(result3[ATTR_LIFETIME_DOWNLOAD] - 0.050) < 0.000001  # 50 MB = 0.050 GB decimal
    assert abs(result3[ATTR_LIFETIME_UPLOAD] - 0.025) < 0.000001  # 25 MB = 0.025 GB decimal


async def test_lifetime_data_with_no_prior_storage(hass: HomeAssistant):
    """Test coordinator starts with zero lifetime data when no storage exists."""
    mock_client = Mock()
    mock_client.run_speed_test = AsyncMock(return_value={
        "download": 100,
        "upload": 50,
        "ping": 10,
        "bytes_received": 0,
        "bytes_sent": 0,
    })
    
    # Clear any existing storage
    with patch("custom_components.librespeed.Store.async_load", return_value=None):
        coordinator = LibreSpeedDataUpdateCoordinator(
            hass=hass,
            client=mock_client,
            server_id=None,
            custom_server=None,
            scan_interval=60,
            auto_update=False,
            skip_cert_verify=False,
            backend_type="native",
        )
        
        await coordinator._async_load_lifetime_data()
        
        # Should start at zero
        assert coordinator.lifetime_download == 0.0
        assert coordinator.lifetime_upload == 0.0


async def test_lifetime_data_storage_error_handling(hass: HomeAssistant):
    """Test that storage errors don't crash the coordinator."""
    mock_client = Mock()
    mock_client.run_speed_test = AsyncMock(return_value={
        "download": 100,
        "upload": 50,
        "ping": 10,
        "bytes_received": 1_000_000,
        "bytes_sent": 500_000,
    })
    
    # Simulate storage load error
    with patch("custom_components.librespeed.Store.async_load", side_effect=Exception("Storage error")):
        coordinator = LibreSpeedDataUpdateCoordinator(
            hass=hass,
            client=mock_client,
            server_id=None,
            custom_server=None,
            scan_interval=60,
            auto_update=False,
            skip_cert_verify=False,
            backend_type="native",
        )
        
        await coordinator._async_load_lifetime_data()
        
        # Should handle error gracefully and start at zero
        assert coordinator.lifetime_download == 0.0
        assert coordinator.lifetime_upload == 0.0
    
    # Simulate storage save error - should not crash
    with patch.object(coordinator._store, "async_save", side_effect=Exception("Save error")):
        result = await coordinator._async_update_data()
        
        # Should still work even if save fails
        assert result is not None
        assert abs(coordinator.lifetime_download - 0.001) < 0.0000001  # 1 MB = 0.001 GB decimal
        assert abs(coordinator.lifetime_upload - 0.0005) < 0.0000001  # 0.5 MB = 0.0005 GB decimal