"""Tests for sensor attributes."""
from unittest.mock import Mock
from datetime import datetime
import pytest
from homeassistant.core import HomeAssistant
from homeassistant.helpers.update_coordinator import DataUpdateCoordinator

from custom_components.librespeed.sensor import (
    LibreSpeedSensor,
    SENSOR_TYPES,
)
from custom_components.librespeed.const import (
    DOMA<PERSON>,
    ATTR_DOWNLOAD,
    ATTR_UPLOAD,
    ATTR_PING,
    ATTR_JITTER,
    ATTR_SERVER_NAME,
    ATTR_TIMESTAMP,
    ATTR_BYTES_SENT,
    ATTR_BYTES_RECEIVED,
)


@pytest.fixture
def mock_coordinator():
    """Create a mock coordinator with test data."""
    coordinator = Mock(spec=DataUpdateCoordinator)
    coordinator.data = {
        "download": 100.5,
        "upload": 50.25,
        "ping": 10.75,
        "jitter": 2.5,
        "server_name": "Test Server",
        "server_location": "Test Location",
        "server_sponsor": "Test Sponsor",
        "timestamp": datetime(2024, 1, 1, 12, 0, 0),
        "bytes_sent": 1048576,  # 1 MB
        "bytes_received": 10485760,  # 10 MB
    }
    # Add config_entry mock
    coordinator.config_entry = <PERSON>ck()
    coordinator.config_entry.entry_id = "test_entry"
    coordinator.entry_title = "LibreSpeed Test"
    return coordinator


async def test_last_test_sensor_attributes(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test LastTestSensor extra attributes."""
    # Find the last_test sensor description
    last_test_desc = next(d for d in SENSOR_TYPES if d.key == "last_test")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, last_test_desc, entry)
    
    # Test with data
    assert sensor.native_value == datetime(2024, 1, 1, 12, 0, 0)
    # last_test sensor doesn't have extra attributes in current implementation
    attrs = sensor.extra_state_attributes
    assert attrs == {}
    
    # Test with no data
    mock_coordinator.data = None
    assert sensor.native_value is None
    assert sensor.extra_state_attributes == {}


async def test_server_sensor_attributes(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test ServerSensor extra attributes."""
    # Find the server sensor description
    server_desc = next(d for d in SENSOR_TYPES if d.key == "server_name")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, server_desc, entry)
    
    # Test with data
    assert sensor.native_value == "Test Server"
    attrs = sensor.extra_state_attributes
    assert attrs["location"] == "Test Location"
    assert attrs["sponsor"] == "Test Sponsor"
    
    # Test with no data
    mock_coordinator.data = None
    assert sensor.native_value is None
    assert sensor.extra_state_attributes == {}
    
    # Test with partial data - should return "Unknown" for missing fields
    mock_coordinator.data = {"server_name": "Server Only"}
    assert sensor.native_value == "Server Only"
    attrs = sensor.extra_state_attributes
    assert attrs["location"] == "Unknown"
    assert attrs["sponsor"] == "Unknown"


async def test_data_uploaded_sensor(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test DataUploadedSensor value formatting."""
    # Find the data_uploaded sensor description
    data_uploaded_desc = next(d for d in SENSOR_TYPES if d.key == "data_uploaded")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, data_uploaded_desc, entry)
    
    # Test with data (1 MB in bytes converts to 1.05 MB)
    assert sensor.native_value == 1.05
    
    # Test with no data
    mock_coordinator.data = None
    assert sensor.native_value is None
    
    # Test with zero
    mock_coordinator.data = {"bytes_sent": 0}
    assert sensor.native_value == 0


async def test_data_downloaded_sensor(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test DataDownloadedSensor value formatting."""
    # Find the data_downloaded sensor description
    data_downloaded_desc = next(d for d in SENSOR_TYPES if d.key == "data_downloaded")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, data_downloaded_desc, entry)
    
    # Test with data (10 MB in bytes converts to 10.49 MB)
    assert sensor.native_value == 10.49
    
    # Test with no data
    mock_coordinator.data = None
    assert sensor.native_value is None
    
    # Test with zero
    mock_coordinator.data = {"bytes_received": 0}
    assert sensor.native_value == 0


async def test_download_sensor_rounding(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test DownloadSensor value rounding."""
    # Find the download sensor description
    download_desc = next(d for d in SENSOR_TYPES if d.key == "download")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, download_desc, entry)
    
    # Test with float value
    assert sensor.native_value == 100.5
    
    # Test extra attributes for download sensor (line 207-209)
    attrs = sensor.extra_state_attributes
    assert "bytes_received" in attrs
    assert attrs["bytes_received"] == 10485760
    
    # Test with integer value
    mock_coordinator.data = {"download": 100}
    assert sensor.native_value == 100
    
    # Test with very small value
    mock_coordinator.data = {"download": 0.01}
    assert sensor.native_value == 0.01


async def test_upload_sensor_rounding(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test UploadSensor value rounding."""
    # Find the upload sensor description
    upload_desc = next(d for d in SENSOR_TYPES if d.key == "upload")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, upload_desc, entry)
    
    # Test with float value
    assert sensor.native_value == 50.25
    
    # Test extra attributes for upload sensor (line 211-213)
    attrs = sensor.extra_state_attributes
    assert "bytes_sent" in attrs
    assert attrs["bytes_sent"] == 1048576
    
    # Test with integer value
    mock_coordinator.data = {"upload": 50}
    assert sensor.native_value == 50
    
    # Test with very small value
    mock_coordinator.data = {"upload": 0.01}
    assert sensor.native_value == 0.01


async def test_ping_sensor_rounding(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test PingSensor value rounding."""
    # Find the ping sensor description
    ping_desc = next(d for d in SENSOR_TYPES if d.key == "ping")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, ping_desc, entry)
    
    # Test with float value
    assert sensor.native_value == 10.75
    
    # Test with integer value
    mock_coordinator.data = {"ping": 10}
    assert sensor.native_value == 10
    
    # Test with very small value
    mock_coordinator.data = {"ping": 0.1}
    assert sensor.native_value == 0.1


async def test_jitter_sensor_rounding(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test JitterSensor value rounding."""
    # Find the jitter sensor description
    jitter_desc = next(d for d in SENSOR_TYPES if d.key == "jitter")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, jitter_desc, entry)
    
    # Test with float value
    assert sensor.native_value == 2.5
    
    # Test with integer value
    mock_coordinator.data = {"jitter": 2}
    assert sensor.native_value == 2
    
    # Test with very small value
    mock_coordinator.data = {"jitter": 0.01}
    assert sensor.native_value == 0.01