"""Test session lifecycle management."""
from unittest.mock import Mock, patch, AsyncMock, MagicMock
import pytest
import aiohttp
from homeassistant.core import HomeAssistant
from homeassistant.config_entries import ConfigEntry

from custom_components.librespeed import async_setup_entry, async_unload_entry
from custom_components.librespeed.const import DOMAIN


async def test_cli_backend_no_session_created(hass: HomeAssistant, create_mock_entry):
    """Test that CLI backend doesn't create an unused session."""
    mock_entry = create_mock_entry(
        entry_id="test",
        title="LibreSpeed Test",
        data={"backend_type": "cli"},
        options={},
        runtime_data=None
    )
    
    # Track if ClientSession was created
    session_created = []
    original_client_session = aiohttp.ClientSession
    
    def mock_client_session(*args, **kwargs):
        """Track session creation."""
        session = MagicMock()
        session.close = AsyncMock()
        session_created.append(session)
        return session
    
    with patch("custom_components.librespeed.aiohttp.ClientSession", side_effect=mock_client_session):
        with patch("custom_components.librespeed.LibreSpeedCLI") as mock_cli_class:
            mock_cli = mock_cli_class.return_value
            mock_cli.ensure_cli_available = AsyncMock(return_value=True)
            
            with patch("homeassistant.config_entries.ConfigEntries.async_forward_entry_setups", AsyncMock()):
                with patch.object(mock_entry, "add_update_listener", return_value=lambda: None):
                    with patch.object(mock_entry, "async_on_unload"):
                        await async_setup_entry(hass, mock_entry)
    
    # CLI backend should NOT create a session
    assert len(session_created) == 0, "CLI backend should not create an HTTP session"
    
    # Verify runtime_data has no session
    assert mock_entry.runtime_data.session is None


async def test_native_backend_session_lifecycle(hass: HomeAssistant, create_mock_entry):
    """Test that native backend properly creates and closes session."""
    mock_entry = create_mock_entry(
        entry_id="test",
        title="LibreSpeed Test",
        data={"backend_type": "native"},
        options={},
        runtime_data=None
    )
    
    # Track session lifecycle
    session_created = []
    session_closed = []
    
    def mock_client_session(*args, **kwargs):
        """Create a mock session with lifecycle tracking."""
        session = MagicMock()
        async def mock_close():
            session_closed.append(session)
        session.close = AsyncMock(side_effect=mock_close)
        session_created.append(session)
        return session
    
    with patch("custom_components.librespeed.aiohttp.ClientSession", side_effect=mock_client_session):
        with patch("custom_components.librespeed.aiohttp.TCPConnector"):
            with patch("custom_components.librespeed.LibreSpeedClient"):
                with patch("homeassistant.config_entries.ConfigEntries.async_forward_entry_setups", AsyncMock()):
                    with patch.object(mock_entry, "add_update_listener", return_value=lambda: None):
                        with patch.object(mock_entry, "async_on_unload"):
                            await async_setup_entry(hass, mock_entry)
    
    # Native backend should create exactly one session
    assert len(session_created) == 1, "Native backend should create exactly one HTTP session"
    
    # Verify runtime_data has the session
    assert mock_entry.runtime_data.session is not None
    assert mock_entry.runtime_data.session == session_created[0]
    
    # Now test unload
    with patch("homeassistant.config_entries.ConfigEntries.async_unload_platforms", AsyncMock(return_value=True)):
        await async_unload_entry(hass, mock_entry)
    
    # Session should be closed
    assert len(session_closed) == 1, "Session should be closed on unload"
    assert session_closed[0] == session_created[0], "The same session that was created should be closed"


async def test_native_backend_session_closed_on_failed_unload(hass: HomeAssistant):
    """Test that session is still closed even if platform unload fails."""
    mock_entry = Mock(spec=ConfigEntry)
    mock_entry.entry_id = "test"
    mock_entry.data = {
        "backend_type": "native",
    }
    mock_entry.options = {}
    
    # Create a mock session
    mock_session = MagicMock()
    mock_session.close = AsyncMock()
    
    # Create mock coordinator
    mock_coordinator = Mock()
    mock_coordinator._async_save_lifetime_data = AsyncMock()
    
    from custom_components.librespeed import LibreSpeedRuntimeData
    mock_entry.runtime_data = LibreSpeedRuntimeData(
        coordinator=mock_coordinator,
        session=mock_session
    )
    
    # Simulate platform unload failure
    with patch("homeassistant.config_entries.ConfigEntries.async_unload_platforms", AsyncMock(return_value=False)):
        result = await async_unload_entry(hass, mock_entry)
    
    # Unload should fail
    assert result is False
    
    # But session should NOT be closed (only closes on successful unload)
    mock_session.close.assert_not_called()


async def test_session_error_handling(hass: HomeAssistant):
    """Test that session creation errors are handled properly."""
    mock_entry = Mock(spec=ConfigEntry)
    mock_entry.entry_id = "test"
    mock_entry.data = {
        "backend_type": "native",
    }
    mock_entry.options = {}
    mock_entry.runtime_data = None
    
    from homeassistant.exceptions import ConfigEntryNotReady
    
    # Simulate session creation failure
    with patch("custom_components.librespeed.aiohttp.ClientSession", side_effect=Exception("Connection error")):
        with patch("custom_components.librespeed.aiohttp.TCPConnector"):
            with pytest.raises(ConfigEntryNotReady) as exc_info:
                await async_setup_entry(hass, mock_entry)
    
    assert "Failed to initialize HTTP session" in str(exc_info.value)