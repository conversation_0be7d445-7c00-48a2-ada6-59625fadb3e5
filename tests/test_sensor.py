"""Test LibreSpeed sensor platform."""
from datetime import datetime
from unittest.mock import Mock, patch

import pytest
from homeassistant.components.sensor import (
    ATTR_STATE_CLASS,
    SensorDeviceClass,
    SensorStateClass,
)
from homeassistant.const import (
    ATTR_DEVICE_CLASS,
    ATTR_ICON,
    ATTR_UNIT_OF_MEASUREMENT,
    UnitOfDataRate,
    UnitOfInformation,
    UnitOfTime,
)
from homeassistant.core import HomeAssistant
from homeassistant.helpers import entity_registry as er
from homeassistant.helpers.entity import EntityCategory

from custom_components.librespeed.const import DOMAIN


@pytest.fixture
def mock_coordinator():
    """Create a mock coordinator with test data."""
    coordinator = Mock()
    coordinator.data = {
        "download": 100.5,
        "upload": 50.2,
        "ping": 10.1,
        "jitter": 2.5,
        "server_name": "Test Server",
        "server_location": "Test Location",
        "timestamp": datetime(2024, 1, 1, 12, 0, 0),
        "bytes_sent": 62914560,
        "bytes_received": 125829120,
    }
    # Add config_entry mock
    coordinator.config_entry = Mock()
    coordinator.config_entry.entry_id = "test_entry"
    coordinator.entry_title = "LibreSpeed Test"
    return coordinator


async def test_sensor_setup(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test sensor setup."""
    entry = create_mock_entry(entry_id="test_entry")
    
    with patch.dict(hass.data, {DOMAIN: {"test_entry": mock_coordinator}}):
        with patch(
            "custom_components.librespeed.sensor.async_setup_entry"
        ) as mock_setup:
            await mock_setup(hass, entry, Mock())
            assert mock_setup.called


async def test_download_speed_sensor(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test download speed sensor."""
    from custom_components.librespeed.sensor import LibreSpeedSensor, SENSOR_TYPES
    
    # Find download sensor description
    download_desc = next(desc for desc in SENSOR_TYPES if desc.key == "download")
    entry = create_mock_entry(entry_id="test_entry")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, download_desc, entry)
    
    assert sensor.native_value == 100.5
    assert sensor.native_unit_of_measurement == UnitOfDataRate.MEGABITS_PER_SECOND
    assert sensor.device_class == SensorDeviceClass.DATA_RATE
    assert sensor.state_class == SensorStateClass.MEASUREMENT
    # Icon is now provided via icons.json, not hardcoded


async def test_upload_speed_sensor(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test upload speed sensor."""
    from custom_components.librespeed.sensor import LibreSpeedSensor, SENSOR_TYPES
    
    upload_desc = next(desc for desc in SENSOR_TYPES if desc.key == "upload")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, upload_desc, entry)
    
    assert sensor.native_value == 50.2
    assert sensor.native_unit_of_measurement == UnitOfDataRate.MEGABITS_PER_SECOND
    assert sensor.device_class == SensorDeviceClass.DATA_RATE
    assert sensor.state_class == SensorStateClass.MEASUREMENT


async def test_ping_sensor(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test ping sensor."""
    from custom_components.librespeed.sensor import LibreSpeedSensor, SENSOR_TYPES
    
    ping_desc = next(desc for desc in SENSOR_TYPES if desc.key == "ping")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, ping_desc, entry)
    
    assert sensor.native_value == 10.1
    assert sensor.native_unit_of_measurement == UnitOfTime.MILLISECONDS
    assert sensor.state_class == SensorStateClass.MEASUREMENT


async def test_data_transferred_sensors(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test data downloaded and uploaded sensors."""
    from custom_components.librespeed.sensor import LibreSpeedSensor, SENSOR_TYPES
    
    entry = create_mock_entry(entry_id="test_entry")
    
    # Test downloaded data
    download_data_desc = next(desc for desc in SENSOR_TYPES if desc.key == "data_downloaded")
    download_sensor = LibreSpeedSensor(mock_coordinator, download_data_desc, entry)
    
    # Should convert bytes to MB
    assert download_sensor.native_value == 125.83  # 125829120 bytes / 1_000_000
    assert download_sensor.native_unit_of_measurement == UnitOfInformation.MEGABYTES
    assert download_sensor.entity_category == EntityCategory.DIAGNOSTIC
    
    # Test uploaded data
    upload_data_desc = next(desc for desc in SENSOR_TYPES if desc.key == "data_uploaded")
    upload_sensor = LibreSpeedSensor(mock_coordinator, upload_data_desc, entry)
    
    assert upload_sensor.native_value == 62.91  # 62914560 bytes / 1_000_000
    assert upload_sensor.native_unit_of_measurement == UnitOfInformation.MEGABYTES
    assert upload_sensor.entity_category == EntityCategory.DIAGNOSTIC


async def test_server_name_sensor(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test server name sensor."""
    from custom_components.librespeed.sensor import LibreSpeedSensor, SENSOR_TYPES
    
    server_desc = next(desc for desc in SENSOR_TYPES if desc.key == "server_name")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, server_desc, entry)
    
    assert sensor.native_value == "Test Server"
    assert sensor.entity_category == EntityCategory.DIAGNOSTIC


async def test_sensor_no_data(hass: HomeAssistant, create_mock_entry):
    """Test sensor with no data available."""
    from custom_components.librespeed.sensor import LibreSpeedSensor, SENSOR_TYPES
    
    coordinator = Mock()
    coordinator.data = None
    
    download_desc = next(desc for desc in SENSOR_TYPES if desc.key == "download")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(coordinator, download_desc, entry)
    
    assert sensor.native_value is None


async def test_sensor_device_info(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test sensor device info."""
    from custom_components.librespeed.sensor import LibreSpeedSensor, SENSOR_TYPES
    
    download_desc = next(desc for desc in SENSOR_TYPES if desc.key == "download")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, download_desc, entry)
    
    device_info = sensor.device_info
    assert device_info["identifiers"] == {(DOMAIN, "test_entry")}
    assert device_info["manufacturer"] == "LibreSpeed"
    assert device_info["name"] == "LibreSpeed Test"


async def test_sensor_unique_id(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test sensor unique IDs."""
    from custom_components.librespeed.sensor import LibreSpeedSensor, SENSOR_TYPES
    
    # Check that each sensor has a unique ID
    unique_ids = set()
    for desc in SENSOR_TYPES:
        entry = create_mock_entry(entry_id="test_entry")
        sensor = LibreSpeedSensor(mock_coordinator, desc, entry)
        assert sensor.unique_id not in unique_ids
        unique_ids.add(sensor.unique_id)
        assert sensor.unique_id.startswith("test_entry_")