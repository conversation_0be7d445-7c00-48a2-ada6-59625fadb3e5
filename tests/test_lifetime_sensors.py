"""Tests for lifetime download/upload sensors."""
from unittest.mock import Mock, AsyncMock
import pytest
from homeassistant.core import HomeAssistant

from custom_components.librespeed.sensor import (
    LibreSpeedSensor,
    SENSOR_TYPES,
)
from custom_components.librespeed.const import (
    ATTR_LIFETIME_DOWNLOAD,
    ATTR_LIFETIME_UPLOAD,
)


async def test_lifetime_download_sensor(hass: HomeAssistant, create_mock_entry):
    """Test lifetime download sensor."""
    mock_coordinator = Mock()
    mock_coordinator.data = {
        ATTR_LIFETIME_DOWNLOAD: 1.23456,  # GB
    }
    
    # Find the lifetime_download sensor description
    lifetime_download_desc = next(d for d in SENSOR_TYPES if d.key == "lifetime_download")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, lifetime_download_desc, entry)
    
    # Should return raw value in GB
    assert sensor.native_value == 1.23456
    
    # Test with larger value
    mock_coordinator.data = {
        ATTR_LIFETIME_DOWNLOAD: 120.45678,  # GB
    }
    assert sensor.native_value == 120.45678
    
    # Test with small value (less than 1 GB)
    mock_coordinator.data = {
        ATTR_LIFETIME_DOWNLOAD: 0.0423,  # GB
    }
    assert sensor.native_value == 0.0423


async def test_lifetime_upload_sensor(hass: HomeAssistant, create_mock_entry):
    """Test lifetime upload sensor."""
    mock_coordinator = Mock()
    mock_coordinator.data = {
        ATTR_LIFETIME_UPLOAD: 5.67890,  # GB
    }
    
    # Find the lifetime_upload sensor description
    lifetime_upload_desc = next(d for d in SENSOR_TYPES if d.key == "lifetime_upload")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, lifetime_upload_desc, entry)
    
    # Should return raw value in GB
    assert sensor.native_value == 5.67890
    
    # Test with no data
    mock_coordinator.data = None
    assert sensor.native_value is None
    
    # Test with zero
    mock_coordinator.data = {
        ATTR_LIFETIME_UPLOAD: 0,
    }
    assert sensor.native_value == 0


async def test_lifetime_sensors_accumulation(hass: HomeAssistant):
    """Test that lifetime sensors accumulate data correctly."""
    from custom_components.librespeed import LibreSpeedDataUpdateCoordinator
    
    mock_client = Mock()
    mock_client.run_speed_test = AsyncMock(return_value={
        "download": 100,
        "upload": 50,
        "ping": 10,
        "bytes_received": 10_000_000,  # 10 MB
        "bytes_sent": 5_000_000,  # 5 MB
    })
    
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=None,
        custom_server=None,
        scan_interval=60,
        auto_update=False,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    # Initial values should be 0
    assert coordinator.lifetime_download == 0.0
    assert coordinator.lifetime_upload == 0.0
    
    # Run a speed test
    result = await coordinator._async_update_data()
    
    # Should have accumulated the data (now in GB using decimal units)
    assert abs(coordinator.lifetime_download - 0.010) < 0.000001  # 10 MB = 0.010 GB
    assert abs(coordinator.lifetime_upload - 0.005) < 0.000001  # 5 MB = 0.005 GB
    assert abs(result[ATTR_LIFETIME_DOWNLOAD] - 0.010) < 0.000001
    assert abs(result[ATTR_LIFETIME_UPLOAD] - 0.005) < 0.000001
    
    # Run another test
    mock_client.run_speed_test = AsyncMock(return_value={
        "download": 150,
        "upload": 75,
        "ping": 8,
        "bytes_received": 20_000_000,  # 20 MB
        "bytes_sent": 10_000_000,  # 10 MB
    })
    
    result = await coordinator._async_update_data()
    
    # Should have accumulated more data (now in GB using decimal units)
    # 10 MB + 20 MB = 30 MB = 0.030 GB
    # 5 MB + 10 MB = 15 MB = 0.015 GB
    assert abs(coordinator.lifetime_download - 0.030) < 0.000001  # 30 MB = 0.030 GB
    assert abs(coordinator.lifetime_upload - 0.015) < 0.000001  # 15 MB = 0.015 GB
    assert abs(result[ATTR_LIFETIME_DOWNLOAD] - 0.030) < 0.000001
    assert abs(result[ATTR_LIFETIME_UPLOAD] - 0.015) < 0.000001