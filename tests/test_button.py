"""Test LibreSpeed button platform."""
from unittest.mock import AsyncMock, Mock, patch

import pytest
from homeassistant.core import HomeAssistant

from custom_components.librespeed.const import DOMAIN


@pytest.fixture
def mock_coordinator():
    """Create a mock coordinator."""
    import asyncio
    coordinator = Mock()
    coordinator.is_running = False
    coordinator.is_waiting = False
    coordinator.async_refresh = AsyncMock()
    coordinator.async_manual_speed_test = AsyncMock()
    coordinator.entry_title = "LibreSpeed Test"
    # Create a real lock for testing
    coordinator._global_lock = asyncio.Lock()
    # Add config_entry mock
    coordinator.config_entry = Mock()
    coordinator.config_entry.entry_id = "test_entry"
    return coordinator


async def test_button_setup(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test button setup."""
    entry = Mock()
    entry.entry_id = "test_entry"
    
    with patch.dict(hass.data, {DOMAIN: {"test_entry": mock_coordinator}}):
        with patch(
            "custom_components.librespeed.button.async_setup_entry"
        ) as mock_setup:
            await mock_setup(hass, entry, Mock())
            assert mock_setup.called


async def test_button_press(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test button press triggers speed test."""
    from custom_components.librespeed.button import LibreSpeedButton
    
    entry = create_mock_entry(entry_id="test_entry")
    button = LibreSpeedButton(mock_coordinator, entry)
    
    # Test successful press
    await button.async_press()
    mock_coordinator.async_refresh.assert_called_once()


async def test_button_press_already_running(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test button press when test is already running."""
    from custom_components.librespeed.button import LibreSpeedButton
    
    mock_coordinator.is_running = True
    entry = create_mock_entry(entry_id="test_entry")
    button = LibreSpeedButton(mock_coordinator, entry)
    
    # Should not trigger refresh when already running
    await button.async_press()
    mock_coordinator.async_refresh.assert_not_called()


async def test_button_availability(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test button availability based on running state."""
    from custom_components.librespeed.button import LibreSpeedButton
    
    entry = create_mock_entry(entry_id="test_entry")
    button = LibreSpeedButton(mock_coordinator, entry)
    
    # Available when not running (lock not held)
    mock_coordinator.is_running = False
    mock_coordinator.is_waiting = False
    assert button.available is True
    
    # Unavailable when running (lock is held)
    mock_coordinator.is_running = True
    async with mock_coordinator._global_lock:
        assert button.available is False
    
    # Available again after lock released
    assert button.available is True
    
    # Unavailable when waiting for lock
    mock_coordinator.is_waiting = True
    assert button.available is False


async def test_button_press_error_handling(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test button press error handling."""
    from custom_components.librespeed.button import LibreSpeedButton
    
    entry = create_mock_entry(entry_id="test_entry")
    button = LibreSpeedButton(mock_coordinator, entry)
    mock_coordinator.async_refresh.side_effect = Exception("Test error")
    
    # Should raise the exception
    with pytest.raises(Exception, match="Test error"):
        await button.async_press()


async def test_button_attributes(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test button attributes."""
    from custom_components.librespeed.button import LibreSpeedButton
    
    entry = create_mock_entry(entry_id="test_entry")
    button = LibreSpeedButton(mock_coordinator, entry)
    
    assert button.name == "Run Speed Test"
    assert button.icon == "mdi:speedometer"
    assert button.unique_id == "test_entry_run_test"


async def test_button_device_info(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test button device info."""
    from custom_components.librespeed.button import LibreSpeedButton
    
    entry = create_mock_entry(entry_id="test_entry")
    button = LibreSpeedButton(mock_coordinator, entry)
    
    device_info = button.device_info
    assert device_info["identifiers"] == {(DOMAIN, "test_entry")}
    assert device_info["manufacturer"] == "LibreSpeed"
    assert device_info["name"] == "LibreSpeed Test"