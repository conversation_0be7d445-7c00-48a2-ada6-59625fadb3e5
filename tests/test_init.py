"""Test LibreSpeed integration setup."""
from unittest.mock import Async<PERSON><PERSON>, Mock, patch

import pytest
from homeassistant.config_entries import ConfigEntryState
from homeassistant.core import HomeAssistant

from custom_components.librespeed.const import (
    CONF_AUTO_UPDATE,
    CONF_BACKEND_TYPE,
    CONF_SCAN_INTERVAL,
    CONF_SERVER_ID,
    DOMAIN,
)

from tests.conftest import mock_librespeed_client


async def test_setup_entry(hass: HomeAssistant, mock_librespeed_client):
    """Test setup of config entry."""
    entry = Mock()
    entry.entry_id = "test_entry"
    entry.data = {
        CONF_SERVER_ID: None,
        CONF_AUTO_UPDATE: True,
        CONF_SCAN_INTERVAL: 60,
        CONF_BACKEND_TYPE: "native",
    }
    entry.options = {}

    with patch(
        "custom_components.librespeed.LibreSpeedClient",
        return_value=mock_librespeed_client,
    ), patch(
        "custom_components.librespeed.async_setup_entry",
        return_value=True,
    ) as mock_setup, patch(
        "homeassistant.core.HomeAssistant.config_entries",
    ):
        result = await mock_setup(hass, entry)
        assert result is True


async def test_unload_entry(hass: HomeAssistant):
    """Test unloading of config entry."""
    entry = Mock()
    entry.entry_id = "test_entry"
    entry.data = {
        CONF_SERVER_ID: None,
        CONF_AUTO_UPDATE: True,
        CONF_SCAN_INTERVAL: 60,
        CONF_BACKEND_TYPE: "native",
    }

    with patch(
        "custom_components.librespeed.async_unload_entry",
        return_value=True,
    ) as mock_unload:
        result = await mock_unload(hass, entry)
        assert result is True


async def test_setup_entry_with_cli_backend(hass: HomeAssistant):
    """Test setup with CLI backend."""
    entry = Mock()
    entry.entry_id = "test_entry"
    entry.data = {
        CONF_SERVER_ID: None,
        CONF_AUTO_UPDATE: True,
        CONF_SCAN_INTERVAL: 60,
        CONF_BACKEND_TYPE: "cli",
    }
    entry.options = {}

    mock_cli = Mock()
    mock_cli.ensure_cli_available = AsyncMock(return_value=True)

    with patch(
        "custom_components.librespeed.LibreSpeedCLI",
        return_value=mock_cli,
    ), patch(
        "custom_components.librespeed.async_setup_entry",
        return_value=True,
    ) as mock_setup:
        result = await mock_setup(hass, entry)
        assert result is True


async def test_coordinator_update(hass: HomeAssistant):
    """Test coordinator data update."""
    from custom_components.librespeed import LibreSpeedDataUpdateCoordinator

    mock_client = Mock()
    mock_client.run_speed_test = AsyncMock(return_value={
        "download": 100.5,
        "upload": 50.2,
        "ping": 10.1,
        "bytes_received": 125829120,
        "bytes_sent": 62914560,
        "server": {"name": "Test Server", "location": "Test Location"},
        "timestamp": "2024-01-01T12:00:00Z",
    })

    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=None,
        custom_server=None,
        scan_interval=60,
        auto_update=False,
        skip_cert_verify=False,
        backend_type="native",
    )

    # Test successful update
    result = await coordinator._async_update_data()
    assert result["download"] == 100.5
    assert result["upload"] == 50.2
    assert result["ping"] == 10.1
    assert coordinator.is_running is False
    
    # Verify lifetime data is accumulated in the result
    assert "lifetime_download" in result
    assert "lifetime_upload" in result
    assert abs(result["lifetime_download"] - 0.12582912) < 0.00001  # In GB (125.82912 MB = 0.12582912 GB decimal)
    assert abs(result["lifetime_upload"] - 0.06291456) < 0.00001  # In GB (62.91456 MB = 0.06291456 GB decimal)


async def test_coordinator_manual_speed_test(hass: HomeAssistant):
    """Test manual speed test execution."""
    from custom_components.librespeed import LibreSpeedDataUpdateCoordinator

    mock_client = Mock()
    mock_client.run_speed_test = AsyncMock(return_value={
        "download": 200.0,
        "upload": 100.0,
        "ping": 5.0,
        "bytes_received": 251658240,
        "bytes_sent": 125829120,
        "server": {"name": "Test Server", "location": "Test Location"},
        "timestamp": "2024-01-01T12:00:00Z",
    })

    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=1,
        custom_server=None,
        scan_interval=60,
        auto_update=False,
        skip_cert_verify=False,
        backend_type="native",
    )

    # Run manual test
    await coordinator.async_run_speedtest()
    
    # Verify the client was called with correct parameters
    mock_client.run_speed_test.assert_called_once_with(
        server_id=1,
        custom_server_url=None,
        timeout=240,
    )