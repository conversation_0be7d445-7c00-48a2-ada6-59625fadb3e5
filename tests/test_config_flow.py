"""Test the LibreSpeed config flow."""
from unittest.mock import AsyncMock, patch

import pytest
from homeassistant import config_entries
from homeassistant.core import HomeAssistant
from homeassistant.data_entry_flow import FlowResultType

from custom_components.librespeed.const import (
    CONF_AUTO_UPDATE,
    CONF_BACKEND_TYPE,
    CONF_CUSTOM_SERVER,
    CONF_SCAN_INTERVAL,
    CONF_SERVER_ID,
    CONF_SKIP_CERT_VERIFY,
    DEFAULT_UPDATE_INTERVAL,
    DOMAIN,
)


async def test_form_automatic_server(hass: HomeAssistant, mock_setup_entry, mock_get_server_list):
    """Test we can setup with automatic server selection."""
    result = await hass.config_entries.flow.async_init(
        DOMAIN, context={"source": config_entries.SOURCE_USER}
    )
    assert result["type"] == FlowResultType.FORM
    assert result["errors"] == {}

    with patch(
        "custom_components.librespeed.config_flow.LibreSpeedCLI.is_cli_supported",
        return_value=False,
    ):
        result2 = await hass.config_entries.flow.async_configure(
            result["flow_id"],
            {
                "server_selection": "automatic",
                CONF_AUTO_UPDATE: True,
                CONF_SCAN_INTERVAL: 60,
                CONF_BACKEND_TYPE: "native",
            },
        )
        await hass.async_block_till_done()

    assert result2["type"] == FlowResultType.CREATE_ENTRY
    assert result2["title"] == "LibreSpeed (Automatic)"
    assert result2["data"] == {
        CONF_SERVER_ID: None,
        CONF_CUSTOM_SERVER: None,
        CONF_AUTO_UPDATE: True,
        CONF_SCAN_INTERVAL: 60,
        CONF_BACKEND_TYPE: "native",
    }


async def test_form_specific_server(hass: HomeAssistant, mock_setup_entry, mock_get_server_list):
    """Test we can setup with specific server selection."""
    result = await hass.config_entries.flow.async_init(
        DOMAIN, context={"source": config_entries.SOURCE_USER}
    )
    assert result["type"] == FlowResultType.FORM

    with patch(
        "custom_components.librespeed.config_flow.LibreSpeedCLI.is_cli_supported",
        return_value=False,
    ):
        result2 = await hass.config_entries.flow.async_configure(
            result["flow_id"],
            {
                "server_selection": "1",
                CONF_AUTO_UPDATE: False,
                CONF_SCAN_INTERVAL: 120,
                CONF_BACKEND_TYPE: "native",
            },
        )
        await hass.async_block_till_done()

    assert result2["type"] == FlowResultType.CREATE_ENTRY
    assert result2["data"] == {
        CONF_SERVER_ID: 1,
        CONF_CUSTOM_SERVER: None,
        CONF_AUTO_UPDATE: False,
        CONF_SCAN_INTERVAL: 120,
        CONF_BACKEND_TYPE: "native",
    }


async def test_form_custom_server(hass: HomeAssistant, mock_setup_entry, mock_get_server_list):
    """Test we can setup with custom server."""
    result = await hass.config_entries.flow.async_init(
        DOMAIN, context={"source": config_entries.SOURCE_USER}
    )
    assert result["type"] == FlowResultType.FORM

    with patch(
        "custom_components.librespeed.config_flow.LibreSpeedCLI.is_cli_supported",
        return_value=False,
    ):
        # Select custom server option
        result2 = await hass.config_entries.flow.async_configure(
            result["flow_id"],
            {
                "server_selection": "custom",
                CONF_AUTO_UPDATE: True,
                CONF_SCAN_INTERVAL: 60,
                CONF_BACKEND_TYPE: "native",
            },
        )

    assert result2["type"] == FlowResultType.FORM
    assert result2["step_id"] == "custom_server"

    # Enter custom server URL
    result3 = await hass.config_entries.flow.async_configure(
        result2["flow_id"],
        {
            CONF_CUSTOM_SERVER: "https://speedtest.example.com",
            CONF_SKIP_CERT_VERIFY: True,
        },
    )
    await hass.async_block_till_done()

    assert result3["type"] == FlowResultType.CREATE_ENTRY
    assert result3["data"] == {
        CONF_SERVER_ID: None,
        CONF_CUSTOM_SERVER: "https://speedtest.example.com",
        CONF_AUTO_UPDATE: True,
        CONF_SCAN_INTERVAL: 60,
        CONF_BACKEND_TYPE: "native",
        CONF_SKIP_CERT_VERIFY: True,
    }


async def test_form_custom_server_empty_url(hass: HomeAssistant, mock_get_server_list):
    """Test custom server with empty URL shows error."""
    result = await hass.config_entries.flow.async_init(
        DOMAIN, context={"source": config_entries.SOURCE_USER}
    )

    with patch(
        "custom_components.librespeed.config_flow.LibreSpeedCLI.is_cli_supported",
        return_value=False,
    ):
        # Select custom server option
        result2 = await hass.config_entries.flow.async_configure(
            result["flow_id"],
            {
                "server_selection": "custom",
                CONF_AUTO_UPDATE: True,
                CONF_SCAN_INTERVAL: 60,
                CONF_BACKEND_TYPE: "native",
            },
        )

    # Try to submit empty URL
    result3 = await hass.config_entries.flow.async_configure(
        result2["flow_id"],
        {
            CONF_CUSTOM_SERVER: "",
            CONF_SKIP_CERT_VERIFY: False,
        },
    )

    assert result3["type"] == FlowResultType.FORM
    assert result3["step_id"] == "custom_server"
    assert result3["errors"] == {CONF_CUSTOM_SERVER: "custom_server_required"}


async def test_form_with_cli_backend(hass: HomeAssistant, mock_setup_entry, mock_get_server_list):
    """Test setup with CLI backend when supported."""
    result = await hass.config_entries.flow.async_init(
        DOMAIN, context={"source": config_entries.SOURCE_USER}
    )

    with patch(
        "custom_components.librespeed.config_flow.LibreSpeedCLI.is_cli_supported",
        return_value=True,
    ):
        # Form should show CLI option when supported
        result2 = await hass.config_entries.flow.async_configure(
            result["flow_id"],
            {
                "server_selection": "automatic",
                CONF_AUTO_UPDATE: True,
                CONF_SCAN_INTERVAL: 60,
                CONF_BACKEND_TYPE: "cli",
            },
        )
        await hass.async_block_till_done()

    assert result2["type"] == FlowResultType.CREATE_ENTRY
    assert result2["data"][CONF_BACKEND_TYPE] == "cli"


async def test_options_flow(hass: HomeAssistant, mock_get_server_list):
    """Test options flow."""
    # Create a mock config entry and add it to Home Assistant
    from pytest_homeassistant_custom_component.common import MockConfigEntry
    
    entry = MockConfigEntry(
        version=1,
        domain=DOMAIN,
        title="LibreSpeed",
        data={
            CONF_SERVER_ID: None,
            CONF_CUSTOM_SERVER: None,
            CONF_AUTO_UPDATE: True,
            CONF_SCAN_INTERVAL: 60,
            CONF_BACKEND_TYPE: "native",
        },
        source=config_entries.SOURCE_USER,
        entry_id="test",
    )
    entry.add_to_hass(hass)

    with patch(
        "custom_components.librespeed.config_flow.LibreSpeedCLI.is_cli_supported",
        return_value=False,
    ):
        result = await hass.config_entries.options.async_init(entry.entry_id)

    assert result["type"] == FlowResultType.FORM

    result2 = await hass.config_entries.options.async_configure(
        result["flow_id"],
        {
            "server_selection": "1",
            CONF_AUTO_UPDATE: False,
            CONF_SCAN_INTERVAL: 120,
            CONF_BACKEND_TYPE: "native",
        },
    )

    assert result2["type"] == FlowResultType.CREATE_ENTRY
    assert result2["data"] == {
        CONF_SERVER_ID: 1,
        CONF_CUSTOM_SERVER: None,
        CONF_AUTO_UPDATE: False,
        CONF_SCAN_INTERVAL: 120,
        CONF_BACKEND_TYPE: "native",
        CONF_SKIP_CERT_VERIFY: False,
    }