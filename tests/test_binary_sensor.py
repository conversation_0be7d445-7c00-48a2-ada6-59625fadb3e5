"""Test LibreSpeed binary sensor platform."""
from unittest.mock import Mock, patch

import pytest
from homeassistant.components.binary_sensor import BinarySensorDeviceClass
from homeassistant.core import HomeAssistant
from homeassistant.helpers.entity import EntityCategory

from custom_components.librespeed.const import DOMAIN


@pytest.fixture
def mock_coordinator():
    """Create a mock coordinator."""
    import asyncio
    coordinator = Mock()
    coordinator.is_running = False
    coordinator.is_waiting = False
    coordinator.entry_title = "LibreSpeed Test"
    # Create a real lock for testing
    coordinator._global_lock = asyncio.Lock()
    # Add config_entry mock
    coordinator.config_entry = Mock()
    coordinator.config_entry.entry_id = "test_entry"
    return coordinator


async def test_binary_sensor_setup(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test binary sensor setup."""
    entry = Mock()
    entry.entry_id = "test_entry"
    
    with patch.dict(hass.data, {DOMAIN: {"test_entry": mock_coordinator}}):
        with patch(
            "custom_components.librespeed.binary_sensor.async_setup_entry"
        ) as mock_setup:
            await mock_setup(hass, entry, Mock())
            assert mock_setup.called


async def test_binary_sensor_state(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test binary sensor state reflects running status."""
    from custom_components.librespeed.binary_sensor import LibreSpeedRunningSensor
    
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedRunningSensor(mock_coordinator, entry)
    
    # Test when not running (lock not held)
    mock_coordinator.is_running = False
    assert sensor.is_on is False
    
    # Test when running (lock is held)
    mock_coordinator.is_running = True
    async with mock_coordinator._global_lock:
        # Lock is acquired, should be True
        assert sensor.is_on is True
    
    # After releasing lock, should be False again
    assert sensor.is_on is False


async def test_binary_sensor_icon(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test binary sensor icon changes based on state."""
    from custom_components.librespeed.binary_sensor import LibreSpeedRunningSensor
    
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedRunningSensor(mock_coordinator, entry)
    
    # Icon when not running (lock not held)
    mock_coordinator.is_running = False
    assert sensor.icon == "mdi:speedometer-slow"
    
    # Icon when running (lock is held)
    mock_coordinator.is_running = True
    async with mock_coordinator._global_lock:
        assert sensor.icon == "mdi:speedometer"
    
    # After releasing lock
    assert sensor.icon == "mdi:speedometer-slow"


async def test_binary_sensor_attributes(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test binary sensor attributes."""
    from custom_components.librespeed.binary_sensor import LibreSpeedRunningSensor
    
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedRunningSensor(mock_coordinator, entry)
    
    assert sensor.name == "Speed Test Running"
    assert sensor.device_class == BinarySensorDeviceClass.RUNNING
    assert sensor.entity_category == EntityCategory.DIAGNOSTIC
    assert sensor.unique_id == "test_entry_running"
    assert sensor.should_poll is False


async def test_binary_sensor_coordinator_update(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test binary sensor handles coordinator updates."""
    from custom_components.librespeed.binary_sensor import LibreSpeedRunningSensor
    
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedRunningSensor(mock_coordinator, entry)
    sensor.async_write_ha_state = Mock()
    
    # Trigger coordinator update
    sensor._handle_coordinator_update()
    
    # Should write state
    sensor.async_write_ha_state.assert_called_once()


async def test_binary_sensor_device_info(hass: HomeAssistant, mock_coordinator, create_mock_entry):
    """Test binary sensor device info."""
    from custom_components.librespeed.binary_sensor import LibreSpeedRunningSensor
    
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedRunningSensor(mock_coordinator, entry)
    
    device_info = sensor.device_info
    assert device_info["identifiers"] == {(DOMAIN, "test_entry")}
    assert device_info["manufacturer"] == "LibreSpeed"
    assert device_info["name"] == "LibreSpeed Test"