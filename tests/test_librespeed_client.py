"""Test LibreSpeed client functionality."""
from unittest.mock import AsyncMock, Mo<PERSON>, patch
import asyncio

import pytest
import aiohttp
from homeassistant.core import HomeAssistant

from custom_components.librespeed.librespeed_client import LibreSpeedClient


@pytest.fixture
def mock_session():
    """Create a mock aiohttp session."""
    session = Mock(spec=aiohttp.ClientSession)
    return session


async def test_get_servers_success(mock_session):
    """Test successful server list retrieval."""
    client = LibreSpeedClient(mock_session)
    
    # Mock successful response
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.json = AsyncMock(return_value=[
        {
            "id": 1,
            "name": "Server 1",
            "server": "https://server1.com",
            "sponsor": "Sponsor 1"
        },
        {
            "id": 2,
            "name": "Server 2",
            "server": "//server2.com",  # Protocol-less URL
            "sponsor": "Sponsor 2"
        },
        {
            "id": 3,
            "name": "Server 3",
            "server": "server3.com",  # No protocol
            "sponsor": "Sponsor 3"
        }
    ])
    
    # Mock get to return context manager
    mock_response.__aenter__ = AsyncMock(return_value=mock_response)
    mock_response.__aexit__ = AsyncMock(return_value=None)
    mock_session.get = Mock(return_value=mock_response)
    
    servers = await client.get_servers()
    
    assert len(servers) == 3
    assert servers[0]["server"] == "https://server1.com"
    assert servers[1]["server"] == "https://server2.com"  # Protocol added
    assert servers[2]["server"] == "https://server3.com"  # Protocol added


async def test_get_servers_failure(mock_session):
    """Test server list retrieval failure."""
    client = LibreSpeedClient(mock_session)
    
    # Mock failed response
    mock_response = AsyncMock()
    mock_response.__aenter__ = AsyncMock(side_effect=aiohttp.ClientError("Network error"))
    mock_response.__aexit__ = AsyncMock(return_value=None)
    mock_session.get = Mock(return_value=mock_response)
    
    servers = await client.get_servers()
    
    # Should return default servers on failure
    assert len(servers) == 1
    assert servers[0]["name"] == "LibreSpeed Test Server"


async def test_get_best_server(mock_session):
    """Test finding the best server based on latency."""
    client = LibreSpeedClient(mock_session)
    
    # Pre-populate servers
    client.servers = [
        {"id": 1, "name": "Server 1", "server": "https://server1.com"},
        {"id": 2, "name": "Server 2", "server": "https://server2.com"},
        {"id": 3, "name": "Server 3", "server": "https://server3.com"},
    ]
    
    # Mock latency tests
    latencies = [50, 20, 100]  # Server 2 has lowest latency
    with patch.object(client, "_test_latency", side_effect=latencies):
        best_server = await client.get_best_server()
    
    assert best_server["id"] == 2
    assert best_server["name"] == "Server 2"


async def test_test_latency_success(mock_session):
    """Test successful latency measurement."""
    client = LibreSpeedClient(mock_session)
    
    # Mock successful response
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.read = AsyncMock(return_value=b"")
    
    # Mock get to return context manager
    mock_response.__aenter__ = AsyncMock(return_value=mock_response)
    mock_response.__aexit__ = AsyncMock(return_value=None)
    mock_session.get = Mock(return_value=mock_response)
    
    with patch("custom_components.librespeed.librespeed_client.time.time") as mock_time:
        mock_time.side_effect = [0, 0.05]  # 50ms latency
        
        latency = await client._test_latency("https://test.server.com")
    
    assert latency == 50.0  # Milliseconds


async def test_test_latency_failure(mock_session):
    """Test latency measurement failure."""
    client = LibreSpeedClient(mock_session)
    
    # Mock failed response
    mock_response = AsyncMock()
    mock_response.__aenter__ = AsyncMock(side_effect=aiohttp.ClientError("Connection failed"))
    mock_response.__aexit__ = AsyncMock(return_value=None)
    mock_session.get = Mock(return_value=mock_response)
    
    latency = await client._test_latency("https://test.server.com")
    
    assert latency == float('inf')


async def test_run_speed_test_with_custom_server(mock_session):
    """Test speed test with custom server URL."""
    client = LibreSpeedClient(mock_session)
    
    # Mock backend detection
    with patch.object(client, "_detect_backend_type", return_value="php"):
        # Mock latency test
        with patch.object(client, "_test_latency", return_value=10.0):
            # Mock download test
            with patch.object(client, "_test_download", return_value=(100.5, 10485760)):
                # Mock upload test
                with patch.object(client, "_test_upload", return_value=(50.25, 5242880)):
                    result = await client.run_speed_test(
                        custom_server_url="https://custom.server.com"
                    )
    
    assert result["server"]["name"] == "Custom Server"
    assert result["server"]["server"] == "https://custom.server.com"
    assert result["download"] == 100.5
    assert result["upload"] == 50.25
    assert result["ping"] == 10.0
    assert result["bytes_received"] == 10485760
    assert result["bytes_sent"] == 5242880


async def test_run_speed_test_with_server_id(mock_session):
    """Test speed test with specific server ID."""
    client = LibreSpeedClient(mock_session)
    
    # Pre-populate servers
    client.servers = [
        {"id": 1, "name": "Server 1", "server": "https://server1.com"},
        {"id": 2, "name": "Server 2", "server": "https://server2.com"},
    ]
    
    # Mock tests
    with patch.object(client, "_test_latency", return_value=15.0):
        with patch.object(client, "_test_download", return_value=(80.0, 8388608)):
            with patch.object(client, "_test_upload", return_value=(40.0, 4194304)):
                result = await client.run_speed_test(server_id=2)
    
    assert result["server"]["id"] == 2
    assert result["server"]["name"] == "Server 2"
    assert result["download"] == 80.0
    assert result["upload"] == 40.0


async def test_run_speed_test_server_not_found(mock_session):
    """Test speed test when server ID is not found."""
    from custom_components.librespeed.exceptions import ServerNotFoundError
    
    client = LibreSpeedClient(mock_session)
    
    # Mock get_servers to set empty server list
    async def mock_get_servers():
        client.servers = []
        return []
    
    with patch.object(client, 'get_servers', side_effect=mock_get_servers):
        with pytest.raises(ServerNotFoundError, match="Server ID 999 not found"):
            await client.run_speed_test(server_id=999)


async def test_download_chunk_success(mock_session):
    """Test successful chunk download."""
    client = LibreSpeedClient(mock_session)
    
    # Mock response with data
    mock_response = AsyncMock()
    mock_response.status = 200
    
    # Create async generator for chunked content
    async def chunk_generator():
        yield b"x" * 1024  # 1KB
        yield b"y" * 2048  # 2KB
    
    mock_response.content.iter_chunked = Mock(return_value=chunk_generator())
    
    # Mock get to return context manager
    mock_response.__aenter__ = AsyncMock(return_value=mock_response)
    mock_response.__aexit__ = AsyncMock(return_value=None)
    mock_session.get = Mock(return_value=mock_response)
    
    bytes_received = await client._download_chunk("https://test.server.com/garbage")
    
    assert bytes_received == 3072  # 3KB total


async def test_download_chunk_timeout(mock_session):
    """Test chunk download timeout."""
    client = LibreSpeedClient(mock_session)
    
    # Mock timeout
    mock_session.get = AsyncMock(side_effect=asyncio.TimeoutError())
    
    bytes_received = await client._download_chunk("https://test.server.com/garbage")
    
    assert bytes_received == 0


async def test_custom_server_url_handling(mock_session):
    """Test that custom server URLs are handled correctly."""
    client = LibreSpeedClient(mock_session)
    
    # Mock responses for testing
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.read = AsyncMock(return_value=b"")
    mock_response.__aenter__ = AsyncMock(return_value=mock_response)
    mock_response.__aexit__ = AsyncMock(return_value=None)
    
    # Test 1: Custom server with Rust backend (e.g., /rs)
    # Mock detection to return Rust backend
    with patch.object(client, '_detect_backend_type', return_value='rust'):
        with patch.object(client, '_test_download', return_value=(100.0, 1000000)):
            with patch.object(client, '_test_upload', return_value=(50.0, 500000)):
                with patch.object(client, '_test_latency', return_value=10.0):
                    mock_session.get = Mock(return_value=mock_response)
                    mock_session.post = Mock(return_value=mock_response)
                    
                    result = await client.run_speed_test(custom_server_url="https://speedtest.example.com/rs")
                    
                    # Check that server object was created correctly for Rust backend
                    assert result['server']['server'] == "https://speedtest.example.com/rs"
                    assert result['server']['dlURL'] == "garbage"   # No .php extension
                    assert result['server']['ulURL'] == "empty"     # No .php extension
                    assert result['server']['pingURL'] == "empty"   # No .php extension
    
    # Test 2: Custom server with PHP backend
    # Mock detection to return PHP backend
    with patch.object(client, '_detect_backend_type', return_value='php'):
        with patch.object(client, '_test_download', return_value=(100.0, 1000000)):
            with patch.object(client, '_test_upload', return_value=(50.0, 500000)):
                with patch.object(client, '_test_latency', return_value=10.0):
                    mock_session.get = Mock(return_value=mock_response)
                    mock_session.post = Mock(return_value=mock_response)
                    
                    result = await client.run_speed_test(custom_server_url="https://speedtest.example.com")
                    
                    # Check that server object was created correctly with backend paths
                    assert result['server']['server'] == "https://speedtest.example.com"
                    assert result['server']['dlURL'] == "backend/garbage.php"  # Has backend/ prefix and .php
                    assert result['server']['ulURL'] == "backend/empty.php"    # Has backend/ prefix and .php
                    assert result['server']['pingURL'] == "backend/empty.php"  # Has backend/ prefix and .php


async def test_backend_detection(mock_session):
    """Test backend type detection."""
    client = LibreSpeedClient(mock_session)
    
    # Test Rust backend detection
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.__aenter__ = AsyncMock(return_value=mock_response)
    mock_response.__aexit__ = AsyncMock(return_value=None)
    
    # First call succeeds (Rust endpoint)
    mock_session.get = Mock(return_value=mock_response)
    backend_type = await client._detect_backend_type("https://speedtest.example.com/rs", True)
    assert backend_type == 'rust'
    
    # Test PHP backend detection
    # First call fails, second succeeds
    fail_response = AsyncMock()
    fail_response.status = 404
    fail_response.__aenter__ = AsyncMock(return_value=fail_response)
    fail_response.__aexit__ = AsyncMock(return_value=None)
    
    call_count = 0
    def side_effect(*args, **kwargs):
        nonlocal call_count
        call_count += 1
        if call_count == 1:
            # First call (Rust endpoint) fails
            return fail_response
        else:
            # Second call (PHP endpoint) succeeds
            return mock_response
    
    mock_session.get = Mock(side_effect=side_effect)
    backend_type = await client._detect_backend_type("https://speedtest.example.com", False)
    assert backend_type == 'php'


async def test_upload_chunk_success(mock_session):
    """Test successful chunk upload."""
    client = LibreSpeedClient(mock_session)
    
    # Mock response
    mock_response = AsyncMock()
    mock_response.status = 200
    
    # Mock post to return context manager
    mock_response.__aenter__ = AsyncMock(return_value=mock_response)
    mock_response.__aexit__ = AsyncMock(return_value=None)
    mock_session.post = Mock(return_value=mock_response)
    
    test_data = b"x" * 1024  # 1KB
    server = {
        "server": "https://test.server.com",
        "ulURL": "backend/empty.php"
    }
    bytes_sent = await client._upload_chunk(server, test_data)
    
    assert bytes_sent == 1024
    
    # Verify request was made correctly
    mock_session.post.assert_called_once()
    args, kwargs = mock_session.post.call_args
    assert args[0].startswith("https://test.server.com")
    assert kwargs["data"] == test_data


async def test_upload_chunk_failure(mock_session):
    """Test chunk upload failure."""
    client = LibreSpeedClient(mock_session)
    
    # Mock failed response
    mock_response = Mock()
    mock_response.status = 500
    mock_session.post = AsyncMock(return_value=mock_response)
    
    test_data = b"x" * 1024
    server = {
        "server": "https://test.server.com",
        "ulURL": "backend/empty.php"
    }
    bytes_sent = await client._upload_chunk(server, test_data)
    
    assert bytes_sent == 0




async def test_test_download_backend_detection(mock_session):
    """Test download with backend type detection."""
    client = LibreSpeedClient(mock_session)
    
    # Create a mock response with large chunks (Rust-like)
    mock_resp = AsyncMock()
    mock_resp.status = 200
    
    # Large chunks to trigger rust backend detection
    async def large_chunks():
        yield b"x" * (10 * 1024 * 1024)  # 10MB chunk
    
    mock_resp.content.iter_chunked = Mock(return_value=large_chunks())
    
    # Mock get to return context manager
    mock_resp.__aenter__ = AsyncMock(return_value=mock_resp)
    mock_resp.__aexit__ = AsyncMock(return_value=None)
    mock_session.get = Mock(return_value=mock_resp)
    
    # Run test with short duration
    with patch("custom_components.librespeed.librespeed_client.time.time") as mock_time:
        # Provide enough time values for the test to complete
        # The test runs for 15 seconds and checks time multiple times
        time_values = [i * 0.1 for i in range(200)]  # 0, 0.1, 0.2, ... up to 20 seconds
        mock_time.side_effect = time_values
        
        server = {
            "server": "https://test.server.com",
            "dlURL": "backend/garbage.php"
        }
        speed, bytes_received = await client._test_download(server)
    
    # Should have detected Rust backend from large response
    assert hasattr(client, "_detected_backend_type")
    assert client._detected_backend_type == "rust"