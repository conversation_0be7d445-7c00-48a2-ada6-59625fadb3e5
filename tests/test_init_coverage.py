"""Tests to improve coverage for __init__.py."""
from unittest.mock import AsyncMock, Mock, patch, MagicMock
import asyncio
import aiohttp
import pytest
from datetime import datetime
from homeassistant.core import HomeAssistant
from homeassistant.config_entries import ConfigEntry
from homeassistant.helpers.update_coordinator import UpdateFailed

from custom_components.librespeed import (
    LibreSpeedDataUpdateCoordinator,
    async_setup_entry,
    async_unload_entry,
    async_reload_entry,
    get_config_value,
)
from custom_components.librespeed.const import (
    DOMAIN,
    CONF_AUTO_UPDATE,
    CONF_BACKEND_TYPE,
    CONF_SERVER_ID,
    CONF_SCAN_INTERVAL,
)


async def test_cli_backend_setup(hass: HomeAssistant, create_mock_entry):
    """Test setup with CLI backend (lines 173-214)."""
    mock_entry = create_mock_entry(
        entry_id="test_entry",
        title="LibreSpeed Test",
        data={
            CONF_BACKEND_TYPE: "cli",
            CONF_AUTO_UPDATE: True,
            CONF_SCAN_INTERVAL: 60,
            CONF_SERVER_ID: None,
        },
        options={},
    )
    
    # Patch aiohttp.ClientSession to prevent actual session creation
    with patch("custom_components.librespeed.aiohttp.ClientSession") as mock_session_class:
        mock_session = Mock()
        mock_session.close = AsyncMock()
        mock_session_class.return_value = mock_session
        
        with patch("custom_components.librespeed.LibreSpeedCLI") as mock_cli_class:
            mock_cli = Mock()
            mock_cli.check_cli_exists = AsyncMock(return_value=True)
            mock_cli.ensure_cli_available = AsyncMock(return_value=True)
            mock_cli_class.return_value = mock_cli
            
            # Need to patch where it's imported in __init__.py
            with patch("custom_components.librespeed.__init__.LibreSpeedDataUpdateCoordinator") as mock_coord_class:
                mock_coordinator = Mock()
                mock_coordinator.auto_update = True
                mock_coordinator.async_refresh = AsyncMock()
                mock_coordinator._async_load_lifetime_data = AsyncMock()
                mock_coord_class.return_value = mock_coordinator
                
                with patch.object(hass.config_entries, "async_forward_entry_setups", return_value=None):
                    # Test with auto_update=True to trigger delayed refresh
                    with patch("custom_components.librespeed.asyncio.sleep", new_callable=AsyncMock) as mock_sleep:
                        result = await async_setup_entry(hass, mock_entry)
                        
                        assert result is True
                        assert mock_cli.ensure_cli_available.called
                        
                        # Check that runtime_data was set
                        assert mock_entry.runtime_data is not None
                        assert mock_entry.runtime_data.coordinator == mock_coordinator
                        # For CLI backend, session should be None
                        assert mock_entry.runtime_data.session is None


async def test_cli_backend_setup_auto_update_disabled(hass: HomeAssistant, create_mock_entry):
    """Test CLI backend with auto_update disabled (line 200-201)."""
    mock_entry = create_mock_entry(
        entry_id="test_entry",
        title="LibreSpeed Test",
        data={
            CONF_BACKEND_TYPE: "cli",
            CONF_AUTO_UPDATE: False,  # Disabled
            CONF_SCAN_INTERVAL: 60,
            CONF_SERVER_ID: None,
        },
        options={},
    )
    
    # Patch aiohttp.ClientSession to prevent actual session creation
    with patch("custom_components.librespeed.aiohttp.ClientSession") as mock_session_class:
        mock_session = Mock()
        mock_session.close = AsyncMock()
        mock_session_class.return_value = mock_session
        
        with patch("custom_components.librespeed.LibreSpeedCLI") as mock_cli_class:
            mock_cli = Mock()
            mock_cli.check_cli_exists = AsyncMock(return_value=True)
            mock_cli.ensure_cli_available = AsyncMock(return_value=True)
            mock_cli_class.return_value = mock_cli
            
            # Need to patch where it's imported in __init__.py
            with patch("custom_components.librespeed.__init__.LibreSpeedDataUpdateCoordinator") as mock_coord_class:
                mock_coordinator = Mock()
                mock_coordinator.auto_update = False  # Disabled
                mock_coordinator.async_refresh = AsyncMock()
                mock_coordinator._async_load_lifetime_data = AsyncMock()
                mock_coord_class.return_value = mock_coordinator
                
                with patch.object(hass.config_entries, "async_forward_entry_setups", return_value=None):
                    result = await async_setup_entry(hass, mock_entry)
                    
                    assert result is True
                    # async_refresh should NOT be called when auto_update is False
                    mock_coordinator.async_refresh.assert_not_called()


async def test_options_update_listener(hass: HomeAssistant, create_mock_entry):
    """Test options update listener (lines 243-246)."""
    mock_entry = create_mock_entry(entry_id="test_entry")
    
    with patch.object(hass.config_entries, "async_reload") as mock_reload:
        await async_reload_entry(hass, mock_entry)
        mock_reload.assert_called_once_with(mock_entry.entry_id)


async def test_unload_entry_without_session(hass: HomeAssistant, create_mock_entry):
    """Test unloading entry when session is None (lines 259-260)."""
    mock_runtime_data = Mock()
    mock_runtime_data.session = None  # No session to close
    mock_runtime_data.coordinator = Mock()
    mock_runtime_data.coordinator._async_save_lifetime_data = AsyncMock()
    
    mock_entry = create_mock_entry(
        entry_id="test_entry",
        runtime_data=mock_runtime_data
    )
    
    with patch.object(hass.config_entries, "async_unload_platforms", return_value=True):
        result = await async_unload_entry(hass, mock_entry)
        assert result is True
        # Should not raise even though session is None


async def test_coordinator_unexpected_error(hass: HomeAssistant, create_mock_entry):
    """Test unexpected error during speed test (lines 417-420)."""
    mock_client = Mock()
    mock_client.run_speed_test = AsyncMock(side_effect=KeyError("Unexpected error"))
    
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=1,
        custom_server=None,
        scan_interval=60,
        auto_update=True,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    with pytest.raises(UpdateFailed, match="Unexpected error"):
        await coordinator._async_update_data()
    
    # Should only try once for unexpected errors (not retry)
    assert mock_client.run_speed_test.call_count == 1


async def test_coordinator_timeout_after_retries(hass: HomeAssistant, create_mock_entry):
    """Test timeout error message after all retries (line 424)."""
    mock_client = Mock()
    mock_client.run_speed_test = AsyncMock(side_effect=asyncio.TimeoutError())
    
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=1,
        custom_server=None,
        scan_interval=60,
        auto_update=True,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    with patch("custom_components.librespeed.asyncio.sleep"):
        with pytest.raises(UpdateFailed, match="Speed test timed out after multiple attempts"):
            await coordinator._async_update_data()
    
    # Should retry 3 times
    assert mock_client.run_speed_test.call_count == 3


async def test_get_servers_error_handling(hass: HomeAssistant, create_mock_entry):
    """Test server list error handling (lines 451-454)."""
    from custom_components.librespeed.exceptions import NetworkError
    
    mock_client = Mock()
    mock_client.get_servers = AsyncMock(side_effect=NetworkError("Network error"))
    
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=1,
        custom_server=None,
        scan_interval=60,
        auto_update=True,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    servers = await coordinator.async_get_server_list()
    
    # Should return empty list on error
    assert servers == []
    assert mock_client.get_servers.called


async def test_get_config_value_with_none_in_options():
    """Test get_config_value when options has None value."""
    mock_entry = Mock()
    mock_entry.data = {"key": "data_value"}
    mock_entry.options = {"key": None}  # None in options
    
    # When key exists in options (even if None), it takes precedence
    assert get_config_value(mock_entry, "key") is None
    
    # Test when key is not in options - should fall back to data
    mock_entry.data = {"other_key": "data_value"}
    mock_entry.options = {}
    assert get_config_value(mock_entry, "other_key") == "data_value"
    
    # Test with default value when key is missing from both
    mock_entry.data = {}
    mock_entry.options = {}
    assert get_config_value(mock_entry, "missing", "default") == "default"


async def test_coordinator_with_existing_data_on_concurrent_test(hass: HomeAssistant, create_mock_entry):
    """Test concurrent test prevention with global lock."""
    mock_client = Mock()
    # Make the speed test take some time to complete
    async def slow_test(*args, **kwargs):
        await asyncio.sleep(0.3)  # Simulate slow test
        return {
            "download": 100,
            "upload": 50,
            "ping": 10,
            "server": {"name": "Test Server"},
            "timestamp": "2024-01-01T12:00:00Z",
            "bytes_sent": 1000,
            "bytes_received": 2000,
        }
    
    mock_client.run_speed_test = AsyncMock(side_effect=slow_test)
    
    coordinator = LibreSpeedDataUpdateCoordinator(
        hass=hass,
        client=mock_client,
        server_id=1,
        custom_server=None,
        scan_interval=60,
        auto_update=True,
        skip_cert_verify=False,
        backend_type="native",
    )
    
    # Set existing data
    coordinator.data = {"download": 75, "upload": 25}
    
    # Start first test
    task1 = asyncio.create_task(coordinator._async_update_data())
    await asyncio.sleep(0.05)  # Let first test acquire lock
    
    # Second test should wait for lock then run its own test
    task2 = asyncio.create_task(coordinator._async_update_data())
    
    # Both tests complete with new data
    result1 = await task1
    result2 = await task2
    
    # Both should have the new test data
    assert result1["download"] == 100
    assert result2["download"] == 100
    assert mock_client.run_speed_test.call_count == 2