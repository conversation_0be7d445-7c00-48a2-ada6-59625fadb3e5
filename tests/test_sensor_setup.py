"""Tests for sensor setup."""
from unittest.mock import Mock, patch, AsyncMock
import pytest
from homeassistant.core import HomeAssistant
from homeassistant.config_entries import ConfigEntry

from custom_components.librespeed.sensor import async_setup_entry, SENSOR_TYPES
from custom_components.librespeed import LibreSpeedRuntimeData


async def test_sensor_setup(hass: HomeAssistant, create_mock_entry):
    """Test sensor setup creates all sensors (lines 152-155)."""
    # Create mock entry with runtime_data
    mock_entry = create_mock_entry(entry_id="test_entry")
    mock_coordinator = Mock()
    mock_coordinator.data = {}
    
    runtime_data = LibreSpeedRuntimeData(
        coordinator=mock_coordinator,
        session=None
    )
    mock_entry.runtime_data = runtime_data
    
    # Mock async_add_entities
    entities_added = []
    def mock_add_entities(entities):
        # Convert generator to list
        entity_list = list(entities)
        entities_added.extend(entity_list)
    
    await async_setup_entry(hass, mock_entry, mock_add_entities)
    
    # Check that all sensor types were created (now includes 2 lifetime sensors)
    assert len(entities_added) == len(SENSOR_TYPES)
    assert len(entities_added) == 10  # 8 original + 2 lifetime sensors
    
    # Verify each sensor was created with correct description
    for entity, description in zip(entities_added, SENSOR_TYPES):
        assert entity.entity_description == description
        assert entity.coordinator == mock_coordinator


async def test_sensor_coordinator_update_callback(hass: HomeAssistant, create_mock_entry):
    """Test sensor _handle_coordinator_update callback (line 225)."""
    from custom_components.librespeed.sensor import LibreSpeedSensor
    
    mock_coordinator = Mock()
    mock_coordinator.data = {"download": 100}
    
    # Find a sensor description
    download_desc = next(d for d in SENSOR_TYPES if d.key == "download")
    entry = create_mock_entry(entry_id="test_entry")
    sensor = LibreSpeedSensor(mock_coordinator, download_desc, entry)
    
    # Mock async_write_ha_state
    sensor.async_write_ha_state = Mock()
    
    # Call the update handler
    sensor._handle_coordinator_update()
    
    # Verify it called async_write_ha_state
    sensor.async_write_ha_state.assert_called_once()