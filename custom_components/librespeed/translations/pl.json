{"title": "LibreSpeed", "config": {"step": {"user": {"title": "Konfiguruj LibreSpeed", "description": "Skonfiguruj integrację LibreSpeed do testowania prędkości sieci. Ta integracja będzie monitorować prędkość twojego połączenia internetowego w regularnych odstępach czasu.", "data": {"backend_type": "<PERSON><PERSON>", "server_selection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "custom_server": "Niestandardowy URL Serwera", "auto_update": "Włącz Automatyczne Testy Prędkości", "scan_interval": "Interwał Testu (minuty)", "test_timeout": "<PERSON><PERSON> (sekundy)"}, "data_description": {"backend_type": "Wybierz między <PERSON> (domyślny) lub Of<PERSON>lnym CLI dla lepszej wydajności na szybkich połączeniach", "server_selection": "Wybierz 'Automatycznie' dla najlepszego serwera, wybierz konkretny serwer lub 'Niestandardowy' dla własnego", "auto_update": "<PERSON><PERSON>, testy prędkości będą uruchamiane automatycznie w określonym interwale", "scan_interval": "<PERSON><PERSON> c<PERSON><PERSON> uru<PERSON>ć automatyczne testy pr<PERSON><PERSON><PERSON> (minimum 30 minut)", "test_timeout": "Maksymalny czas dozwolony na zakończenie pojedynczego testu pręd<PERSON>ści (60-600 sekund)"}}, "custom_server": {"title": "Konfiguracja <PERSON>ndardowego Serwera", "description": "Wprowadź URL twojego niestandardowego serwera LibreSpeed. Przykład: {example}", "data": {"custom_server": "URL Serwera", "skip_cert_verify": "Zezwalaj na wygasłe lub samopodpisane certyfikaty SSL"}, "data_description": {"custom_server": "Pełny URL do twojego serwera LibreSpeed (musi być HTTPS chyba że weryfikacja certyfikatu jest wyłączona)", "skip_cert_verify": "<PERSON><PERSON><PERSON><PERSON> to jeśli twój serwer używa certyfikatu samopodpisanego lub masz problemy z SSL"}}}, "error": {"cannot_connect": "Nie można połączyć z serwerem LibreSpeed", "invalid_server": "Nieprawidłowy wybór serwera", "custom_server_required": "W<PERSON><PERSON>y jest niestandardowy URL serwera", "invalid_url": "Nieprawidłowy format URL", "ssl_error": "Weryfikacja certyfikatu SSL nie powiodła się", "timeout": "Przekroczono limit czasu połączenia", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł nieoczekiwany błąd"}, "abort": {"already_configured": "LibreSpeed jest ju<PERSON> skonfigurowany", "cannot_download_cli": "Nie udało się pobrać LibreSpeed CLI", "unsupported_platform": "Platforma nieobsługiwana dla backendu CLI"}}, "options": {"step": {"init": {"title": "Op<PERSON>je <PERSON>Speed", "description": "Modyfikuj ustawienia integracji LibreSpeed. Zmiany zostaną zastosowane przy następnym teście prędkości.", "data": {"backend_type": "<PERSON><PERSON>", "server_selection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "custom_server": "Niestandardowy URL Serwera", "auto_update": "Włącz Automatyczne Testy Prędkości", "scan_interval": "Interwał Testu (minuty)", "test_timeout": "<PERSON><PERSON> (sekundy)"}, "data_description": {"backend_type": "Backend CLI zalecany dla połączeń powyżej 500 Mbps", "server_selection": "Zmień serwer używany do testów prędkości", "auto_update": "Włącz lub wyłącz automatyczne zaplanowane testy", "scan_interval": "<PERSON><PERSON><PERSON><PERSON> jak często uruchamiane są testy (w minutach)", "test_timeout": "Maksymalny czas dozwolony na pojedynczy test pręd<PERSON>ści (60-600 sekund)"}}, "custom_server": {"title": "Konfiguracja <PERSON>ndardowego Serwera", "description": "Skonfiguruj swój niestandardowy serwer LibreSpeed. Przykład: {example}", "data": {"custom_server": "URL Serwera", "skip_cert_verify": "Zezwalaj na wygasłe lub samopodpisane certyfikaty SSL"}}}, "error": {"invalid_server": "Nieprawidłowy wybór serwera", "custom_server_required": "W<PERSON><PERSON>y jest niestandardowy URL serwera", "invalid_url": "Nieprawidłowy format URL"}}, "entity": {"sensor": {"download": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "upload": {"name": "Pr<PERSON><PERSON><PERSON><PERSON>ć Wysyłania"}, "ping": {"name": "<PERSON>"}, "jitter": {"name": "Jitter"}, "packet_loss": {"name": "Utrata Pakietów"}, "data_downloaded": {"name": "<PERSON>"}, "data_uploaded": {"name": "<PERSON>"}, "lifetime_download": {"name": "Całkowite Dane <PERSON>"}, "lifetime_upload": {"name": "Całkowite Dane W<PERSON>ła<PERSON>"}, "server_name": {"name": "<PERSON><PERSON><PERSON>"}, "server_location": {"name": "Lokali<PERSON><PERSON>"}, "server_sponsor": {"name": "Sponsor <PERSON><PERSON><PERSON>"}, "last_test": {"name": "Czas Ostatniego Testu"}}, "button": {"run_test": {"name": "Uruchom Test Prędkości"}}, "binary_sensor": {"running": {"name": "Test Prędkości Uruchomiony"}}}, "state_attributes": {"sensor": {"download": {"bytes_received": "<PERSON><PERSON><PERSON>", "test_duration": "Czas Trwania Testu"}, "upload": {"bytes_sent": "<PERSON>jty W<PERSON>łane", "test_duration": "Czas Trwania Testu"}, "ping": {"jitter": "Jitter", "packet_loss": "Utrata Pakietów"}, "server_name": {"server_id": "ID Serwera", "server_sponsor": "Sponsor <PERSON><PERSON><PERSON>", "server_url": "URL Serwera"}}, "binary_sensor": {"running": {"this_instance_running": "Ta Instancja Uruchomiona", "this_instance_waiting": "Ta Instancja Czeka", "instance_name": "Nazwa Instancji"}}, "button": {"run_test": {"last_pressed": "Ostat<PERSON>", "test_available": "Test Dostępny"}}}, "services": {"run_speed_test": {"name": "Uruchom Test Prędkości", "description": "Ręcznie uruchom test prędkości na określonej instancji LibreSpeed", "fields": {"entry_id": {"name": "<PERSON><PERSON>", "description": "Instancja LibreSpeed na której uruchomić test (opcjonalnie, używa pierwszej instancji jeśli nie określono)"}, "server_id": {"name": "ID Serwera", "description": "Opcjonalnie: Konkretny ID serwera do użycia w tym teście (zastępuje skonfigurowany serwer)"}}}}, "exceptions": {"speed_test_in_progress": "Test prędkości już w toku", "speed_test_failed": "Test prędkości nie powiódł się: {error}", "server_not_found": "Serwer nie znaleziony", "network_error": "<PERSON>łąd sieci: {error}", "timeout_error": "Test prędkości przekroczył limit czasu po {timeout} sekundach", "cli_not_available": "LibreSpeed CLI niedostępny", "cli_download_failed": "Nie udało się pobrać pliku binarnego CLI", "invalid_configuration": "Nieprawidłowa konfiguracja", "circuit_breaker_open": "Zbyt wiele kolejnych błędów, wyłącznik automatyczny otwarty", "session_creation_failed": "Nie udało się utworzyć sesji HTTP", "checksum_mismatch": "Suma kontrolna pobranego pliku nie pasuje do oczekiwanej <PERSON>"}, "issues": {"cli_download_failed": {"title": "Pobieranie LibreSpeed CLI Nie Powiodło Się", "description": "Plik binarny LibreSpeed CLI nie mógł zostać pobrany automatycznie. Może to być spowodowane problemami z połączeniem sieciowym lub ograniczeniami zapory sieciowej.\n\nMożesz:\n• Spr<PERSON>bować pobrać ponownie używając przepływu naprawczego\n• Przełączyć się na backend Natywny Python w opcjach\n• Sprawdzić swoje połączenie internetowe\n\nBackend CLI zapewnia lepszą wydajność dla szybkich połączeń (>500 Mbps)."}, "custom_server_unreachable": {"title": "Niestandardowy Serwer LibreSpeed Nieosiągalny", "description": "Twój niestandardowy serwer LibreSpeed pod {server_url} jest obecnie nieosiągalny. Może to być spowodowane:\n\n• Serwer jest tymczasowo wyłączony\n• Problemy z połączeniem sieciowym\n• Nieprawidłowy URL serwera\n• Problemy z certyfikatem SSL\n\nAby rozwiązać ten problem, przej<PERSON><PERSON> do {options_path} i:\n• Zaktualizuj URL serwera\n• Sprawdź ustawienia certyfikatu SSL\n• Przełącz się na automatyczny wybór serwera"}, "repeated_test_failures": {"title": "Testy LibreSpeed Wielokrotnie Zawodzą", "description": "Testy prędkości zawiodły {failure_count} razy z rzędu. To może wskazywać na:\n\n• Problemy z połączeniem sieciowym\n• Problemy z kompatybilnością serwera\n• Problemy z wydajnością backendu\n\nZalecane rozwiązania:\n• {backend_switch}\n• Spróbuj automatycznego wyboru serwera\n• Sprawdź swoje połączenie internetowe\n• Przejrzyj logi Home Assistant dla szczegółów"}, "circuit_breaker_open": {"title": "Wyłącznik Automatyczny LibreSpeed Aktywowany", "description": "Testy prędkości zostały tymczasowo wyłączone po {failure_count} kolejnych błędach aby zapobiec przeciążeniu systemu.\n\n{manual_test}\n\nAby rozwiązać:\n• Sprawdź swoje połączenie sieciowe\n• Zweryfikuj dostępność serwera\n• Przejrzyj logi błędów dla konkretnych problemów\n• Rozważ zmianę backendu"}}, "selector": {"backend_type": {"options": {"native": "Natywny Python", "cli": "Oficjalny CLI"}}, "server_selection": {"options": {"automatic": "Automatyczny", "custom": "Niestandardowy <PERSON>wer"}}}}