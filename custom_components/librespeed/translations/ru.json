{"title": "LibreSpeed", "config": {"step": {"user": {"title": "Настройка LibreSpeed", "description": "Настройте интеграцию LibreSpeed для тестирования скорости сети. Эта интеграция будет контролировать скорость вашего интернет-соединения через регулярные интервалы.", "data": {"backend_type": "Тип <PERSON>нда", "server_selection": "Выбор Сервера", "custom_server": "URL Пользовательского Сервера", "auto_update": "Включить Автоматические Тесты Скорости", "scan_interval": "Интервал Теста (минуты)", "test_timeout": "Таймаут Теста (секунды)"}, "data_description": {"backend_type": "Выберите между Нативным Python (по умолчанию) или Официальным CLI для лучшей производительности на высокоскоростных соединениях", "server_selection": "Выберите 'Автоматически' для лучшего сервера, выберите конкретный сервер или 'Пользовательский' для использования собственного", "auto_update": "Когда включено, тесты скорости будут выполняться автоматически с указанным интервалом", "scan_interval": "Как часто выполнять автоматические тесты скорости (минимум 30 минут)", "test_timeout": "Максимальное время, разрешенное для завершения одного теста скорости (60-600 секунд)"}}, "custom_server": {"title": "Настройка Пользовательского Сервера", "description": "Введите URL вашего пользовательского сервера LibreSpeed. Пример: {example}", "data": {"custom_server": "URL Сервера", "skip_cert_verify": "Разрешить просроченные или самоподписанные SSL-сертификаты"}, "data_description": {"custom_server": "Полный URL вашего сервера LibreSpeed (должен быть HTTPS, если проверка сертификата не отключена)", "skip_cert_verify": "Включите это, если ваш сервер использует самоподписанный сертификат или у вас проблемы с SSL"}}}, "error": {"cannot_connect": "Не удалось подключиться к серверу LibreSpeed", "invalid_server": "Недопустимый выбор сервера", "custom_server_required": "Требуется URL пользовательского сервера", "invalid_url": "Недопустимый формат URL", "ssl_error": "Ошибка проверки SSL-сертификата", "timeout": "Тайм-аут подключения", "unknown": "Произошла неожиданная ошибка"}, "abort": {"already_configured": "LibreSpeed уже настроен", "cannot_download_cli": "Не удалось загрузить LibreSpeed CLI", "unsupported_platform": "Платформа не поддерживается для бэкенда CLI"}}, "options": {"step": {"init": {"title": "Параметры LibreSpeed", "description": "Измените настройки интеграции LibreSpeed. Изменения вступят в силу при следующем тесте скорости.", "data": {"backend_type": "Тип <PERSON>нда", "server_selection": "Выбор Сервера", "custom_server": "URL Пользовательского Сервера", "auto_update": "Включить Автоматические Тесты Скорости", "scan_interval": "Интервал Теста (минуты)", "test_timeout": "Таймаут Теста (секунды)"}, "data_description": {"backend_type": "Бэкенд CLI рекомендуется для соединений свыше 500 Мбит/с", "server_selection": "Изменить сервер, используемый для тестов скорости", "auto_update": "Включить или отключить автоматические запланированные тесты", "scan_interval": "Настроить частоту выполнения тестов (в минутах)", "test_timeout": "Максимальное время, разрешенное для одного теста скорости (60-600 секунд)"}}, "custom_server": {"title": "Настройка Пользовательского Сервера", "description": "Настройте ваш пользовательский сервер LibreSpeed. Пример: {example}", "data": {"custom_server": "URL Сервера", "skip_cert_verify": "Разрешить просроченные или самоподписанные SSL-сертификаты"}}}, "error": {"invalid_server": "Недопустимый выбор сервера", "custom_server_required": "Требуется URL пользовательского сервера", "invalid_url": "Недопустимый формат URL"}}, "entity": {"sensor": {"download": {"name": "Скорость Загрузки"}, "upload": {"name": "Скорость Отдачи"}, "ping": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "jitter": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "packet_loss": {"name": "Потеря Пакетов"}, "data_downloaded": {"name": "Данные Теста Загружены"}, "data_uploaded": {"name": "Данные Теста Отправлены"}, "lifetime_download": {"name": "Всего Данных Загружено"}, "lifetime_upload": {"name": "Всего Данных Отправлено"}, "server_name": {"name": "Имя Сервера"}, "server_location": {"name": "Местоположение Сервера"}, "server_sponsor": {"name": "Спонсор Сервера"}, "last_test": {"name": "Время Последнего Теста"}}, "button": {"run_test": {"name": "Запустить Тест Скорости"}}, "binary_sensor": {"running": {"name": "Тест Скорости Выполняется"}}}, "state_attributes": {"sensor": {"download": {"bytes_received": "Байт Получено", "test_duration": "Длительность Теста"}, "upload": {"bytes_sent": "Байт Отправлено", "test_duration": "Длительность Теста"}, "ping": {"jitter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "packet_loss": "Потеря Пакетов"}, "server_name": {"server_id": "ID Сервера", "server_sponsor": "Спонсор Сервера", "server_url": "URL Сервера"}}, "binary_sensor": {"running": {"this_instance_running": "Этот Экземпляр Выполняется", "this_instance_waiting": "Этот Экземпляр Ожидает", "instance_name": "Имя Экземпляра"}}, "button": {"run_test": {"last_pressed": "Последнее Нажатие", "test_available": "Тест Доступен"}}}, "services": {"run_speed_test": {"name": "Запустить Тест Скорости", "description": "Вручную запустить тест скорости на определенном экземпляре LibreSpeed", "fields": {"entry_id": {"name": "Запись Конфигурации", "description": "Экземпляр LibreSpeed для выполнения теста (необязательно, использует первый экземпляр, если не указан)"}, "server_id": {"name": "ID Сервера", "description": "Необязательно: Конкретный ID сервера для использования в этом тесте (переопределяет настроенный сервер)"}}}}, "exceptions": {"speed_test_in_progress": "Тест скорости уже выполняется", "speed_test_failed": "Тест скорости не удался: {error}", "server_not_found": "Сервер не найден", "network_error": "Сетевая ошибка: {error}", "timeout_error": "Тест скорости превысил время ожидания через {timeout} секунд", "cli_not_available": "LibreSpeed CLI недоступен", "cli_download_failed": "Не удалось загрузить бинарный файл CLI", "invalid_configuration": "Недопустимая конфигурация", "circuit_breaker_open": "Слишком много последовательных сбоев, автоматический выключатель открыт", "session_creation_failed": "Не удалось создать HTTP-сессию", "checksum_mismatch": "Контрольная сумма загруженного файла не соответствует ожидаемому значению"}, "issues": {"cli_download_failed": {"title": "Загрузка LibreSpeed CLI Не Удалась", "description": "Бинарный файл LibreSpeed CLI не удалось загрузить автоматически. Это может быть из-за проблем с сетевым подключением или ограничений брандмауэра.\n\nВы можете:\n• Попробовать загрузить снова через поток восстановления\n• Переключиться на бэкенд Нативный Python в параметрах\n• Проверить ваше интернет-соединение\n\nБэкенд CLI обеспечивает лучшую производительность для высокоскоростных соединений (>500 Мбит/с)."}, "custom_server_unreachable": {"title": "Пользовательский Сервер LibreSpeed Недоступен", "description": "Ваш пользовательский сервер LibreSpeed по адресу {server_url} в настоящее время недоступен. Это может быть из-за:\n\n• Сервер временно отключен\n• Проблемы с сетевым подключением\n• Неправильный URL сервера\n• Проблемы с SSL-сертификатом\n\nДля решения этой проблемы, перейдите в {options_path} и:\n• Обновите URL сервера\n• Проверьте настройки SSL-сертификата\n• Переключитесь на автоматический выбор сервера"}, "repeated_test_failures": {"title": "Тесты LibreSpeed Неоднократно Терпят Неудачу", "description": "Тесты скорости не удались {failure_count} раз подряд. Это может указывать на:\n\n• Проблемы с сетевым подключением\n• Проблемы совместимости сервера\n• Проблемы производительности бэкенда\n\nРекомендуемые решения:\n• {backend_switch}\n• Попробуйте автоматический выбор сервера\n• Проверьте ваше интернет-соединение\n• Просмотрите логи Home Assistant для деталей"}, "circuit_breaker_open": {"title": "Автоматический Выключатель LibreSpeed Активирован", "description": "Тесты скорости были временно отключены после {failure_count} последовательных сбоев для предотвращения перегрузки системы.\n\n{manual_test}\n\nДля решения:\n• Проверьте ваше сетевое подключение\n• Проверьте доступность сервера\n• Просмотрите логи ошибок для конкретных проблем\n• Рассмотрите смену бэкенда"}}, "selector": {"backend_type": {"options": {"native": "Нативный Python", "cli": "Официальный CLI"}}, "server_selection": {"options": {"automatic": "Автоматически", "custom": "Пользовательский Сервер"}}}}